<template>
	<ToastStatus :status="status" :text="statusText" @clear-status="clearStatus()" />
	<div class="flex flex-col p-6 bg-white shadow-sm sm:flex-row items-center justify-between">
		<div>
			<div class="text-3xl sm:text-3xl font-sans font-medium opacity-70 text-ralblack-primary">
				Knowledge
			</div>
			<p class="mt-1 text-gray-500">Help your AI agent understand your brand and customers.</p>
		</div>

		<button class="flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700"
			@click="saveStoreSettings()" :disabled="loading">
			<span v-if="loading" class="w-4 h-4 mr-2 mb-2"><i class="fa fa-spinner fa-spin"></i></span>
			<span v-else class="w-4 h-4 mr-2 mb-2">✨</span>
			Save Knowledge
		</button>
	</div>

	<!-- Tab Navigation -->
	<div class="px-6 mb-4">
		<div class="border-b border-gray-200">
			<nav class="flex -mb-px space-x-8">
				<button @click="activeTab = 'content'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none',
					activeTab === 'content'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Content
				</button>
				<!-- <button @click="activeTab = 'emailBranding'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none',
					activeTab === 'emailBranding'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Email Branding
				</button> -->
				<button v-if="isRaleonAdmin" @click="activeTab = 'emailComponents'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none',
					activeTab === 'emailComponents'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Email Sections
					<span v-if="customComponentCount > 0"
						class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-purple-600 rounded-full">
						{{ customComponentCount }}
					</span>
				</button>
				<button v-if="isRaleonAdmin" @click="activeTab = 'componentPreview'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none',
					activeTab === 'componentPreview'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Section Preview
				</button>
				<button @click="activeTab = 'assets'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none inline-flex items-center',
					activeTab === 'assets'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Assets
					<span v-if="unspecifiedImageCount > 0"
						class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-purple-600 rounded-full">
						{{ unspecifiedImageCount }}
					</span>
				</button>
				<button @click="activeTab = 'memory'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none inline-flex items-center',
					activeTab === 'memory'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Brand Memory
					<span v-if="brandMemories && brandMemories.length > 0"
						class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white bg-purple-600 rounded-full">
						{{ brandMemories.length }}
					</span>
				</button>
				<button v-if="isRaleonAdmin" @click="activeTab = 'tags'" :class="[
					'py-4 px-1 border-b-2 font-medium text-sm focus:outline-none',
					activeTab === 'tags'
						? 'border-purple-600 text-purple-600'
						: 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
				]">
					Tags
				</button>
			</nav>
		</div>
	</div>

	<!-- Content Tab -->
	<div v-if="activeTab === 'content'" class="space-y-6 p-6">
		<!-- Store Name Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Store Information</h2>
				<p class="text-sm text-gray-500">Your store's name and domain information</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">Store Name</label>
					<input v-model="storeName" placeholder="Enter store name"
						class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500" />
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Store Domain</label>
					<div class="relative">
						<span
							class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm pointer-events-none">https://</span>
						<input v-model="storeDomain" placeholder="mystore.myshopify.com" :readonly="shopifyConnected"
							:class="[
								'w-full pl-[3.7rem] pr-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500',
								shopifyConnected ? 'bg-gray-100 text-gray-500 cursor-not-allowed' : ''
							]" />
					</div>
					<p class="text-xs text-gray-500">
						{{ shopifyConnected ? 'Domain is locked because Shopify is connected' : 'Used for Shopify integration and other store connections' }}
					</p>
				</div>
			</div>
		</div>

		<!-- Brand Voice Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Brand Voice & Style</h2>
				<p class="text-sm text-gray-500">Define how your brand communicates</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<textarea v-model="exampleLanguage"
						placeholder="Describe your brand's writing style, common phrases, or words to avoid..."
						class="w-full h-32 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
			</div>
		</div>

		<!-- Custom Fonts Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<div class="flex items-center justify-between">
					<div>
						<h2 class="text-lg font-semibold">Custom Fonts</h2>
						<p class="text-sm text-gray-500">Add custom fonts for your email designs</p>
					</div>
					<button @click="showCustomFonts = !showCustomFonts" :class="[
						'px-4 py-2 text-sm font-medium rounded-lg border transition-colors duration-200',
						showCustomFonts
							? 'bg-gray-100 text-gray-700 border-gray-200'
							: 'bg-purple-600 text-white border-purple-600 hover:bg-purple-700'
					]">
						<span v-if="!showCustomFonts">Add Custom Font</span>
						<span v-else>Hide Custom Font</span>
					</button>
				</div>
			</div>

			<!-- Collapsible Font Settings -->
			<div v-if="showCustomFonts" class="p-6 pt-0 space-y-6 border-t border-gray-100">

				<!-- Upload Method -->
				<div class="space-y-6">
					<!-- Font Family Name Input -->
					<div class="space-y-4">
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Font Family Name</label>
							<input v-model="newFontFamily" type="text" placeholder="e.g., Montserrat, Custom Brand Font"
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500" />
							<p class="text-xs text-gray-500 mt-1">This will be the name used in the font dropdown</p>
						</div>

						<!-- Weight Selection -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Font Weight</label>
							<select v-model="newFontWeight"
								class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
								<option v-for="option in fontWeightOptions" :key="option.value" :value="option.value">
									{{ option.label }} ({{ option.value }})
								</option>
							</select>
						</div>

						<!-- File Upload -->
						<div>
							<label class="block text-sm font-medium text-gray-700 mb-2">Font File</label>
							<div v-if="!selectedFontFile"
								class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors duration-200">
								<input type="file" accept=".ttf,.otf,.woff,.woff2" @change="handleSingleFontUpload"
									class="hidden" ref="singleFontInput" />
								<div @click="$refs.singleFontInput.click()" class="cursor-pointer">
									<svg class="mx-auto h-8 w-8 text-gray-400" fill="none" stroke="currentColor"
										viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
									</svg>
									<p class="mt-2 text-sm text-gray-600">Click to upload font file</p>
									<p class="text-xs text-gray-500">TTF, OTF, WOFF, WOFF2 • Max 5MB</p>
								</div>
							</div>
							<!-- Show selected file -->
							<div v-else class="border-2 border-green-300 bg-green-50 rounded-lg p-4">
								<div class="flex items-center justify-between">
									<div class="flex items-center">
										<svg class="w-8 h-8 text-green-600 mr-3" fill="none" stroke="currentColor"
											viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
												d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
										</svg>
										<div>
											<p class="text-sm font-medium text-green-900">{{ selectedFontFile.name }}
											</p>
											<p class="text-xs text-green-700">{{ Math.round(selectedFontFile.size /
												1024) }} KB</p>
										</div>
									</div>
									<button @click="clearSelectedFile" class="text-green-600 hover:text-green-800">
										<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
												d="M6 18L18 6M6 6l12 12" />
										</svg>
									</button>
								</div>
							</div>
						</div>

						<!-- Upload Button -->
						<button @click="uploadNewFont" :disabled="!canUploadFont || isProcessingFonts" :class="[
							'w-full py-2 px-4 rounded-lg font-medium transition-colors duration-200',
							canUploadFont && !isProcessingFonts
								? 'bg-purple-600 text-white hover:bg-purple-700'
								: 'bg-gray-300 text-gray-500 cursor-not-allowed'
						]">
							<span v-if="isProcessingFonts" class="flex items-center justify-center">
								<svg class="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
									<circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
										stroke-width="4"></circle>
									<path class="opacity-75" fill="currentColor"
										d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
									</path>
								</svg>
								Uploading...
							</span>
							<span v-else>Add Font</span>
						</button>
					</div>
				</div>

				<!-- Existing Custom Fonts List -->
				<div v-if="customFonts.length > 0" class="space-y-4">
					<label class="text-sm font-medium text-gray-700">Current Custom Fonts</label>
					<div class="space-y-3">
						<div v-for="(font, index) in customFonts" :key="index"
							class="p-4 bg-gray-50 rounded-lg border border-gray-200">
							<div class="flex items-center justify-between">
								<div>
									<h4 class="font-medium text-gray-900">{{ font.name }}</h4>
									<p class="text-sm text-gray-600">{{ font.weights.length }} weight(s): {{
										font.weights.map(w => w.weight).join(', ') }}</p>
									<p class="text-xs text-gray-500">CSS: {{ font.cssValue }}</p>
								</div>
								<button @click="removeCustomFont(index)" class="text-red-600 hover:text-red-800 p-1">
									<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
										<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
											d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
									</svg>
								</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- Brand & Product Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Brand & Product Information</h2>
				<p class="text-sm text-gray-500">Help us understand your brand's unique value</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">Brand Summary</label>
					<textarea v-model="storeDescription" placeholder="Provide a brief overview of your brand..."
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Customer Problems Solved</label>
					<textarea v-model="customerProblems"
						placeholder="What key problems does your product solve for customers?"
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
			</div>
		</div>

		<!-- Plan Strategy Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Plan Strategy</h2>
				<p class="text-sm text-gray-500">Define how the AI should approach planning tasks</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<div class="mb-3">
						<label class="text-xs text-gray-600 mb-1">Strategy Templates</label>
						<div class="flex flex-wrap gap-2 mb-2">
							<button v-for="option in strategyOptions" :key="option.value"
								@click="applyStrategyTemplate(option.value)"
								class="px-3 py-1.5 text-sm rounded-lg border" :class="[
									currentStrategyIsTemplate(option.value)
										? 'bg-purple-100 border-purple-300 text-purple-800'
										: 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
								]">
								{{ option.label }}
							</button>
							<button @click="loadCustomStrategy" class="px-3 py-1.5 text-sm rounded-lg border" :class="[
								!currentStrategyIsAnyTemplate()
									? 'bg-purple-100 border-purple-300 text-purple-800'
									: 'bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100'
							]">
								Custom Strategy
							</button>
							<button @click="clearStrategy"
								class="px-3 py-1.5 text-sm rounded-lg border bg-gray-50 border-gray-200 text-gray-700 hover:bg-gray-100">
								Clear
							</button>
						</div>
						<p v-if="lastAppliedTemplate" class="text-xs text-gray-500 mt-1">
							{{ getStrategyDescription(lastAppliedTemplate) }}
						</p>
					</div>
					<div class="relative">
						<textarea v-model="planStrategy"
							placeholder="Define how the AI should approach planning tasks..." maxlength="10000"
							:disabled="isLoadingPlanStrategy"
							class="w-full h-64 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
						<div v-if="isLoadingPlanStrategy"
							class="absolute inset-0 bg-white bg-opacity-60 flex items-center justify-center rounded-lg">
							<div class="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-600 mr-2"></div>
							<span class="text-gray-700 text-sm">Loading strategy...</span>
						</div>
					</div>
					<p class="text-xs text-gray-500">Define the process and rules the AI follows when creating plans.
						Click a template button above to load a pre-defined strategy, or create your own. (Maximum
						10,000 characters)</p>
					<p class="text-xs text-gray-500 mt-1">{{ planStrategy ? planStrategy.length : 0 }}/10000 characters
					</p>
				</div>
			</div>
		</div>

		<!-- Subscription Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Subscription Information</h2>
				<p class="text-sm text-gray-500">Tell us about your subscription model</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">Do you offer subscriptions?</label>
					<div class="flex space-x-4">
						<button @click="hasSubscriptions = true" :class="[
							'px-4 py-2 rounded-lg border',
							hasSubscriptions === true ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							Yes
						</button>
						<button @click="hasSubscriptions = false" :class="[
							'px-4 py-2 rounded-lg border',
							hasSubscriptions === false ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							No
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Influencer Collaboration Section -->
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Influencer Collaboration</h2>
				<p class="text-sm text-gray-500">This helps the AI when considering strategies.</p>
			</div>
			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">Do you work with influencers?</label>
					<select v-model="influencerCollaboration"
						class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
						<option value="yes" selected>Yes, regularly</option>
						<option value="sometimes">Yes, occasionally</option>
						<option value="no">No, not currently</option>
						<option value="planning">Planning to start</option>
					</select>
				</div>
			</div>
		</div>

		<!-- Admin Only Section -->
		<div v-if="isRaleonAdmin" class="bg-white rounded-lg border shadow-sm border-purple-200">
			<div class="p-6">
				<h2 class="text-lg font-semibold text-purple-600">Admin Settings</h2>
				<p class="text-sm text-gray-500">Advanced store configuration options</p>
			</div>

			<!-- Email Templates Section -->
			<div class="bg-white rounded-lg border shadow-sm">
				<div class="p-6">
					<h2 class="text-lg font-semibold">Email Templates & Examples</h2>
					<p class="text-sm text-gray-500">Upload .txt files to help your AI team understand your
						communication
						format and style</p>
				</div>
				<div class="p-6 pt-0 space-y-4">
					<div class="space-y-4">
						<!-- Display uploaded files -->
						<div v-for="(template, index) in emailTemplates" :key="index"
							class="flex items-start space-x-4 p-4 bg-gray-50 rounded-lg">
							<div class="flex-1">
								<p class="font-medium text-sm">{{ template.name }}</p>
								<pre class="mt-2 text-sm text-gray-600 whitespace-pre-wrap">{{ template.content }}</pre>
							</div>
							<button @click="removeTemplate(index)"
								class="p-2 text-gray-400 hover:text-red-600 rounded-lg">
								🗑️
							</button>
						</div>

						<!-- Upload button -->
						<div
							class="flex items-center justify-center border-2 border-dashed border-gray-200 rounded-lg p-6">
							<label class="cursor-pointer">
								<input type="file" class="hidden" multiple accept=".txt" @change="handleFileUpload" />
								<div class="flex flex-col items-center space-y-2">
									<span class="h-8 w-8 text-gray-400">📤</span>
									<span class="text-sm text-gray-500">Upload email templates</span>
									<span class="text-xs text-gray-400">.txt files only</span>
								</div>
							</label>
						</div>
					</div>
				</div>
			</div>

			<div class="p-6 pt-0 space-y-4">
				<div class="space-y-2">
					<label class="text-sm font-medium">Best Sellers</label>
					<textarea v-model="bestSellers" placeholder="List your best-selling products..."
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Product Categories</label>
					<textarea v-model="productCategories" placeholder="List your product categories..."
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">SMS Capability</label>
					<div class="flex space-x-4">
						<button @click="hasSMS = true" :class="[
							'px-4 py-2 rounded-lg border',
							hasSMS ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							Yes
						</button>
						<button @click="hasSMS = false" :class="[
							'px-4 py-2 rounded-lg border',
							hasSMS === false ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							No
						</button>
					</div>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Email Generation Access</label>
					<div class="flex space-x-4">
						<button @click="hasEmailGeneration = true" :class="[
							'px-4 py-2 rounded-lg border',
							hasEmailGeneration ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							Enabled
						</button>
						<button @click="hasEmailGeneration = false" :class="[
							'px-4 py-2 rounded-lg border',
							hasEmailGeneration === false ? 'bg-purple-600 text-white' : 'bg-white hover:bg-gray-50'
						]">
							Disabled
						</button>
					</div>
					<p class="text-xs text-gray-500 mt-1">
						When enabled, users can generate email content with AI assistance
					</p>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Brand Custom Instructions</label>
					<textarea v-model="brandCustomInstructions"
						placeholder="Enter any custom instructions for your brand's AI..."
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
					<p class="text-xs text-gray-500">Special instructions that will guide the AI when generating content
						for your brand.</p>
				</div>
				<div class="space-y-2">
					<label class="text-sm font-medium">Business Goals</label>
					<textarea v-model="businessGoals" placeholder="Describe your business goals..."
						class="w-full h-24 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"></textarea>
				</div>
			</div>
		</div>
	</div>

	<!-- Email Branding Tab -->
	<div v-if="activeTab === 'emailBranding'" class="email-branding-container">
		<!-- Loading state for email branding -->
		<div v-if="isLoadingEmailBranding" class="loading-container">
			<i class="fa fa-spinner fa-spin fa-2x"></i>
			<p>Loading email branding settings...</p>
		</div>

		<!-- Email Branding Editor -->
		<EmailBranding v-else :key="emailBrandingKey" :value="emailBranding" @branding-updated="handleBrandingUpdate"
			class="email-branding-editor" />

	</div>

	<!-- Email Components Tab -->
	<div v-if="activeTab === 'emailComponents'">
		<KnowledgeEmailComponents :hasCustomComponents="hasCustomComponents" :orgId="currentOrg.id" :overrideId="1"
			@update:hasCustomComponents="hasCustomComponents = $event" @status-update="handleStatusUpdate"
			@save-settings="saveStoreSettings" />
	</div>

	<!-- Assets Tab -->
	<div v-else-if="activeTab === 'assets'" class="space-y-6 p-6">
		<BrandImageManager ref="brandImageManager" assetType="email" @status-update="handleStatusUpdate"
			@images-loaded="handleImagesLoaded" :custom-categories="customImageCategories"
			@categories-updated="updateCustomCategories" />
	</div>

	<!-- Brand Memory Tab -->
	<div v-else-if="activeTab === 'memory'" class="space-y-6 p-6">
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Brand Memory</h2>
				<p class="text-sm text-gray-500">Stored memories that help your AI understand your brand's history and
					context. These memories are included in prompts via the {MEMORY} tag.</p>
				<div class="mt-4 flex items-center space-x-4">
					<button @click="loadBrandMemories" :disabled="memoriesLoading"
						class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
						<span v-if="memoriesLoading" class="flex items-center justify-center w-4 h-4">
							<i class="fa fa-spinner fa-spin"></i>
						</span>
						<svg v-else class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
						</svg>
						<span>{{ memoriesLoading ? 'Loading...' : 'Refresh Memories' }}</span>
					</button>
					<button @click="showAddMemoryModal = true"
						class="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 flex items-center space-x-2">
						<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
								d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
						</svg>
						<span>Add Memory</span>
					</button>
				</div>
			</div>
			<div class="p-6 pt-0">
				<!-- Loading State -->
				<div v-if="memoriesLoading" class="space-y-4">
					<div class="text-center py-8">
						<div class="inline-flex items-center space-x-3">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
							<span class="text-gray-600">Loading brand memories...</span>
						</div>
					</div>
				</div>

				<!-- Memories Display -->
				<div v-else-if="brandMemories && brandMemories.length > 0" class="space-y-4">

					<!-- Memories List -->
					<div class="space-y-3">
						<div v-for="(memory, index) in brandMemories" :key="memory.id || index"
							class="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
							<div class="flex items-start justify-between space-x-4">
								<div class="flex-1 min-w-0">
									<div class="flex items-center space-x-2 mb-2">
										<span :class="[
											'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
											memory.type === 'info' ? 'bg-cyan-100 text-cyan-800' :
												memory.type === 'text' ? 'bg-gray-100 text-gray-800' :
													memory.type === 'promotion' ? 'bg-purple-100 text-purple-800' :
														memory.type === 'email' ? 'bg-blue-100 text-blue-800' :
															memory.type === 'brief' ? 'bg-green-100 text-green-800' :
																memory.type === 'preferences' ? 'bg-orange-100 text-orange-800' :
																	'bg-gray-100 text-gray-800'
										]">
											{{ memory.type || 'info' }}
										</span>
										<span class="text-xs text-gray-500">
											{{ memory.timestamp ? formatDate(memory.timestamp) : 'Unknown date' }}
										</span>
									</div>
									<div class="text-sm text-gray-900 whitespace-pre-wrap break-words">
										{{ memory.content || memory.text || 'No content' }}
									</div>
								</div>
								<div class="flex items-start space-x-2 pt-1">
									<button @click="editMemory(index)"
										class="flex items-center justify-center p-2 text-gray-400 hover:text-purple-600 rounded-lg transition-colors"
										title="Edit memory">
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
												d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
										</svg>
									</button>
									<button @click="deleteMemory(index)"
										class="flex items-center justify-center p-2 text-gray-400 hover:text-red-600 rounded-lg transition-colors"
										title="Delete memory">
										<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
											<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
												d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
										</svg>
									</button>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- No Memories State -->
				<div v-else-if="!memoriesLoading" class="text-center py-8">
					<div class="text-gray-500">
						<i class="fa fa-brain text-4xl mb-4"></i>
						<h3 class="text-lg font-medium mb-2">No brand memories found</h3>
						<p class="text-sm mb-4">Add memories to help your AI understand your brand's history and
							context.</p>
						<button @click="showAddMemoryModal = true"
							class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
							Add Your First Memory
						</button>
					</div>
				</div>
			</div>
		</div>

		<!-- Add/Edit Memory Modal -->
		<div v-if="showAddMemoryModal || editingMemoryIndex !== -1"
			class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
			<div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
				<div class="flex justify-between items-center mb-4">
					<h3 class="text-lg font-semibold">
						{{ editingMemoryIndex !== -1 ? 'Edit Memory' : 'Add Memory' }}
					</h3>
					<button @click="closeMemoryModal" class="text-gray-400 hover:text-gray-600">
						✕
					</button>
				</div>

				<div class="space-y-4">
					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Memory Type</label>
						<select v-model="currentMemory.type"
							class="w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500">
							<option value="info">Info - General info about the brand</option>
							<option value="text">Text - General content and messaging</option>
							<option value="promotion">Promotion - Campaign and offer details</option>
							<option value="email">Email - Email templates and design notes</option>
							<option value="brief">Brief - Strategic briefs and planning</option>
							<option value="preferences">Preferences - Brand voice and style rules</option>
						</select>
					</div>

					<div>
						<label class="block text-sm font-medium text-gray-700 mb-2">Content</label>
						<textarea v-model="currentMemory.content"
							placeholder="Enter the memory content that will help your AI understand your brand..."
							class="w-full h-32 px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500"
							maxlength="2000"></textarea>
						<p class="text-xs text-gray-500 mt-1">{{ currentMemory.content ? currentMemory.content.length :
							0 }}/2000 characters</p>
					</div>
				</div>

				<div class="flex justify-end space-x-3 mt-6">
					<button @click="closeMemoryModal"
						class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50">
						Cancel
					</button>
					<button @click="saveMemory" :disabled="!currentMemory.content.trim()"
						class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed">
						{{ editingMemoryIndex !== -1 ? 'Update Memory' : 'Save Memory' }}
					</button>
				</div>
			</div>
		</div>
	</div>

	<!-- Tags Tab -->
	<div v-else-if="activeTab === 'tags'" class="space-y-6 p-6">
		<div class="bg-white rounded-lg border shadow-sm">
			<div class="p-6">
				<h2 class="text-lg font-semibold">Prompt Tags</h2>
				<p class="text-sm text-gray-500">Values currently used for tag replacement in AI prompts</p>
				<div class="mt-4 flex items-center space-x-4">
					<button @click="loadTagValues" :disabled="tagValuesLoading"
						class="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2">
						<span v-if="tagValuesLoading" class="w-4 h-4">
							<i class="fa fa-spinner fa-spin"></i>
						</span>
						<span v-else class="w-4 h-4">🔄</span>
						<span>{{ tagValuesLoading ? 'Loading...' : 'Refresh Tags' }}</span>
					</button>
					<div class="flex items-center space-x-2">
						<input type="checkbox" id="showEmptyTags" v-model="showEmptyTags"
							class="rounded border-gray-300 text-purple-600 focus:ring-purple-500">
						<label for="showEmptyTags" class="text-sm text-gray-600">Show empty tags</label>
					</div>
				</div>
			</div>
			<div class="p-6 pt-0">
				<!-- Enhanced Loading State -->
				<div v-if="tagValuesLoading" class="space-y-4">
					<div class="text-center py-8">
						<div class="inline-flex items-center space-x-3">
							<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
							<span class="text-gray-600">Loading tag values...</span>
						</div>
						<p class="text-sm text-gray-500 mt-2">This may take 30-60 seconds as we process each tag
							individually and gather data from various sources</p>
					</div>
					<!-- Loading skeleton -->
					<div class="space-y-3">
						<div v-for="n in 8" :key="n" class="animate-pulse">
							<div class="flex space-x-4 py-3">
								<div class="h-4 bg-gray-200 rounded w-32"></div>
								<div class="h-4 bg-gray-200 rounded flex-1"></div>
							</div>
						</div>
					</div>
				</div>


				<!-- Tags Display -->
				<div v-else-if="filteredTagValues && filteredTagValues.length > 0" class="space-y-4">
					<!-- Summary -->
					<div class="bg-gray-50 rounded-lg p-4">
						<div class="flex items-center justify-between text-sm">
							<span class="text-gray-600">
								Showing {{ (filteredTagValues && filteredTagValues.length) || 0 }} of {{ (totalTagValues
									&& totalTagValues.length) || 0 }} tags
							</span>
							<span v-if="!showEmptyTags && emptyTagCount > 0" class="text-gray-500">
								{{ emptyTagCount }} empty tags hidden
							</span>
						</div>
					</div>

					<!-- Enhanced Tags Grid -->
					<div class="grid gap-4">
						<div v-for="item in filteredTagValues" :key="item.tag"
							class="border rounded-lg p-4 hover:bg-gray-50 transition-colors">
							<div class="flex items-start space-x-4">
								<div class="flex-shrink-0">
									<code class="bg-purple-100 text-purple-800 px-2 py-1 rounded text-sm font-mono">
                                                                                {{ '{' + item.tag + '}' }}
                                                                        </code>
									<div class="mt-1">
										<span :class="[
											'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
											item.isEmpty ? 'bg-gray-100 text-gray-800' : 'bg-green-100 text-green-800'
										]">
											{{ item.isEmpty ? 'Empty' : 'Has Value' }}
										</span>
									</div>
								</div>
								<div class="flex-1 min-w-0">
									<div class="text-sm text-gray-900 whitespace-pre-wrap break-words">
										<span v-if="item.isEmpty" class="italic text-gray-500">
											No value set for this tag
										</span>
										<span v-else>{{ item.value }}</span>
									</div>
									<div v-if="!item.isEmpty && item.value.length > 100" class="mt-2">
										<span class="text-xs text-gray-500">
											{{ item.value.length }} characters
										</span>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>

				<!-- No Tags State -->
				<div v-else-if="!tagValuesLoading && (!totalTagValues || totalTagValues.length === 0)"
					class="text-center py-8">
					<div class="text-gray-500">
						<i class="fa fa-tags text-4xl mb-4"></i>
						<h3 class="text-lg font-medium mb-2">No tag values found</h3>
						<p class="text-sm">Try refreshing to load the latest tag values.</p>
					</div>
				</div>

				<!-- All Tags Empty State -->
				<div v-else-if="!tagValuesLoading && !showEmptyTags && (!filteredTagValues || filteredTagValues.length === 0)"
					class="text-center py-8">
					<div class="text-gray-500">
						<i class="fa fa-eye-slash text-4xl mb-4"></i>
						<h3 class="text-lg font-medium mb-2">All tags are empty</h3>
						<p class="text-sm mb-4">All {{ (totalTagValues && totalTagValues.length) || 0 }} tags have empty
							values.</p>
						<button @click="showEmptyTags = true"
							class="px-4 py-2 bg-gray-200 text-gray-700 rounded-lg hover:bg-gray-300">
							Show empty tags
						</button>
					</div>
				</div>
			</div>
		</div>
	</div>

	<!-- Component Preview Tab -->
	<div v-else-if="activeTab === 'componentPreview'" class="h-full">
		<KnowledgePreviewComponents :orgId="currentOrg.id" />
	</div>

	<!-- Hidden BrandImageManager to load images even when not on Assets tab -->
	<div v-if="activeTab !== 'assets'" style="display: none;">
		<BrandImageManager ref="hiddenBrandImageManager" assetType="email" @images-loaded="handleImagesLoaded"
			:custom-categories="customImageCategories" />
	</div>

	<!-- Delete Memory Confirmation Modal -->
	<ConfirmModal v-model="showDeleteConfirm" title="Delete Memory"
		message="Are you sure you want to delete this memory? This action cannot be undone." confirmText="Delete"
		cancelText="Cancel" @confirm="confirmDeleteMemory" />

	<!-- No component modal here - moved to KnowledgeEmailComponents -->
</template>

<script>
import * as OrgServices from '../services/organization';
import ToastStatus from '../../client-old/pages/component/ToastStatus.vue';
import * as Utils from '../../client-old/utils/Utils';
import * as OrganizationSettings from '../services/organization-settings.js';
import BrandImageManager from '../components/BrandImageManager.vue';
import EmailBranding from '../components/EmailBranding.vue';
import KnowledgeEmailComponents from '../components/KnowledgeEmailComponents.ts.vue';
import KnowledgePreviewComponents from '../components/KnowledgePreviewComponents.ts.vue';
import ConfirmModal from '../components/common/ConfirmModal.vue';
import { getStrategyOptions, getStrategyByKey } from '../services/prompt/strategies';

const URL_DOMAIN = Utils.URL_DOMAIN;

export default {
	name: 'KnowledgeBase',
	components: {
		ToastStatus,
		BrandImageManager,
		EmailBranding,
		KnowledgeEmailComponents,
		KnowledgePreviewComponents,
		ConfirmModal
	},
	data() {
		return {
			hasSubscriptions: null,
			showIntegrationDialog: false,
			shopifyConnected: false,
			customerProblems: '',
			influencerCollaboration: '',
			emailTemplates: [],
			status: '',
			statusText: '',
			exampleLanguage: '',
			storeDescription: '',
			isRaleonAdmin: false,
			storeName: '',
			storeDomain: '',
			bestSellers: '',
			productCategories: '',
			planStrategy: '',
			originalPlanStrategy: '',
			isLoadingPlanStrategy: false,
			lastAppliedTemplate: 'thematic-month',
			strategyOptions: getStrategyOptions(),
			emailBranding: '',
			hasSMS: false,
			businessGoals: '',
			// New data properties for email component overrides
			hasCustomComponents: false,
			globalEmailComponents: [],
			expandedComponents: {},
			showComponentModal: false,
			currentComponent: {
				id: null,
				name: '',
				description: '',
				json: '',
				type: 'unlayer',
				editableFields: {}
			},
			isLoadingComponents: false,
			jsonError: '',
			editingComponent: false,
			hasEmailGeneration: false,
			brandCustomInstructions: '',
			// New structure for multiple custom fonts
			customFonts: [], // Array of complete font configurations
			showCustomFonts: false,
			isProcessingFonts: false,
			newFontFamily: '',
			newFontWeight: 400,
			selectedFontFile: null,
			fontWeightOptions: [
				{ label: 'Thin', value: 100 },
				{ label: 'Extra Light', value: 200 },
				{ label: 'Light', value: 300 },
				{ label: 'Regular', value: 400 },
				{ label: 'Medium', value: 500 },
				{ label: 'Semi Bold', value: 600 },
				{ label: 'Bold', value: 700 },
				{ label: 'Extra Bold', value: 800 },
				{ label: 'Black', value: 900 }
			],
			activeTab: 'content',
			unspecifiedImageCount: 0,
			// New properties for custom image categories
			customImageCategories: [],
			newCategoryName: '',
			editingCategoryIndex: -1,
			defaultImageCategories: ['Logo', 'Hero Image', 'Banner', 'Product', 'Background', 'Icon', 'Other'],
			isLoadingEmailBranding: false,
			emailBrandingKey: 0,
			componentMode: 'override',
			// New properties for editable fields
			parsedComponentJson: null,
			detectedComponentTypes: [],
			tagValues: [],
			tagValuesLoading: false,
			showEmptyTags: false,
			// Brand Memory properties
			brandMemories: [],
			memoriesLoading: false,
			showAddMemoryModal: false,
			editingMemoryIndex: -1,
			currentMemory: {
				type: 'info',
				content: '',
				timestamp: null
			},
			showDeleteConfirm: false,
			memoryToDelete: -1,
		};
	},
	created() {
	},
	async mounted() {
		const storedUserInfo = localStorage.getItem('userInfo');
		const userInfo = storedUserInfo ? JSON.parse(storedUserInfo) : null;
		this.isRaleonAdmin = userInfo?.roles?.includes('raleon-admin');

		this.currentOrg = await OrgServices.getCurrentOrg();
		this.storeName = this.currentOrg.name || '';
		this.storeDomain = this.parseDomainForDisplay(this.currentOrg.externalDomain || '');
		this.storeDescription = this.currentOrg.description || '';
		this.exampleLanguage = this.currentOrg.sampleLanguage || '';

		this.hasSubscriptions = (await OrganizationSettings.getOrganizationSetting('hasSubscriptions')) === 'true';
		this.customerProblems = await OrganizationSettings.getOrganizationSetting('customerProblems');
		this.influencerCollaboration = await OrganizationSettings.getOrganizationSetting('influencerCollaboration');

		// Set default plan strategy if none exists
		this.isLoadingPlanStrategy = true;
		this.planStrategy = await OrganizationSettings.getOrganizationSetting('planStrategy');
		this.originalPlanStrategy = this.planStrategy;
		this.isLoadingPlanStrategy = false;
		this.lastAppliedTemplate = await OrganizationSettings.getOrganizationSetting('lastAppliedTemplate') || 'thematic-month';
		if (!this.planStrategy) {
			this.applyStrategyTemplate('thematic-month');
		}

		// Fetch existing email templates
		const emailTemplatesString = await OrganizationSettings.getOrganizationSetting('emailTemplates');
		if (emailTemplatesString) {
			// Deserialize the string back into an array
			this.emailTemplates = JSON.parse(emailTemplatesString);
		}

		// Fetch component overrides settings
		this.hasCustomComponents = (await OrganizationSettings.getOrganizationSetting('hasCustomComponents')) === 'true';

		// Load custom image categories
		const customCategoriesString = await OrganizationSettings.getOrganizationSetting('customImageCategories');
		if (customCategoriesString) {
			try {
				this.customImageCategories = JSON.parse(customCategoriesString);
			} catch (error) {
				console.error('Error parsing custom image categories:', error);
				this.customImageCategories = [];
			}
		}

		if (this.isRaleonAdmin) {
			this.bestSellers = await OrganizationSettings.getOrganizationSetting('bestSellers');
			this.productCategories = await OrganizationSettings.getOrganizationSetting('productCategories');

			// Load email branding and verify it loaded correctly
			const emailBrandingValue = await OrganizationSettings.getOrganizationSetting('emailBranding');
			console.log("Loaded email branding from server:", emailBrandingValue);
			try {
				// Try to parse if it's a JSON string
				if (emailBrandingValue && typeof emailBrandingValue === 'string') {
					// Store the actual parsed object, not a string
					const parsedValue = JSON.parse(emailBrandingValue);
					this.emailBranding = parsedValue;
					console.log("Successfully parsed emailBranding into object:", this.emailBranding);
				} else {
					// If there's no value or parsing fails, initialize with a default object structure
					this.emailBranding = {
						colors: {
							emailBackground: '#F7F7F7',
							text: '#1B2443',
							headings: '#1C2545',
							links: '#1155CC',
							contentBackground: '#E8F4E8',
							button: '#1B2443',
							buttonText: '#FFFFFF'
						},
						typography: {
							textFont: 'Courier, sans-serif',
							textSize: 14,
							headingFont: 'Courier, sans-serif',
							fontWeight: 'normal',
							headingWeight: 'heavy',
							lineHeight: 1.1
						}
					};
					console.log("Initialized default emailBranding:", this.emailBranding);
				}
			} catch (error) {
				console.error('Error parsing email branding:', error);
				// Use the original string if parsing fails
				this.emailBranding = emailBrandingValue || '';
			}

			this.hasSMS = (await OrganizationSettings.getOrganizationSetting('hasSMS')) === 'true';
			this.businessGoals = await OrganizationSettings.getOrganizationSetting('businessGoals');
			const adminPlanStrategy = await OrganizationSettings.getOrganizationSetting('planStrategy');
			if (!this.planStrategy) {
				this.planStrategy = adminPlanStrategy;
			}
			this.selectedStrategyTemplate = await OrganizationSettings.getOrganizationSetting('selectedStrategyTemplate') || '';
			this.hasEmailGeneration = (await OrganizationSettings.getOrganizationSetting('hasEmailGeneration')) === 'true';
			this.brandCustomInstructions = await OrganizationSettings.getOrganizationSetting('brandCustomInstructions');

			// Load custom fonts data
			const customFontsString = await OrganizationSettings.getOrganizationSetting('customFonts');
			if (customFontsString) {
				try {
					this.customFonts = JSON.parse(customFontsString);
					// Load CSS for all fonts
					this.customFonts.forEach(fontFamily => {
						if (fontFamily.cssUrl) {
							this.loadFontCSS(fontFamily.cssUrl);
						}
					});
				} catch (error) {
					console.error('Error parsing custom fonts:', error);
					this.customFonts = [];
				}
			}

			// Preload tag values for the admin
			this.loadTagValues();
		}

		// Load brand memories for all users
		this.loadBrandMemories();

		// Check Shopify connection status
		this.checkShopifyConnection();
	},
	methods: {
		clearStatus() {
			this.status = '';
			this.statusText = '';
		},
		parseDomainForDisplay(domain) {
			if (!domain) return '';
			// Remove https:// and http:// for display
			return domain.replace(/^https?:\/\//, '');
		},
		parseDomainForSaving(domain) {
			if (!domain) return '';
			// Remove https:// and http:// and any trailing slashes
			return domain.replace(/^https?:\/\//, '').replace(/\/$/, '');
		},
		async checkShopifyConnection() {
			try {
				const response = await fetch(`${URL_DOMAIN}/integration/shopify/connected`, {
					method: 'GET',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					}
				});
				if (!response.ok) {
					throw new Error('Failed to check Shopify connection');
				}
				const data = await response.json();
				this.shopifyConnected = data.connected;
			} catch (error) {
				console.error('Error checking Shopify connection:', error);
				this.shopifyConnected = false;
			}
		},
		applyStrategyTemplate(templateKey) {
			if (templateKey) {
				this.planStrategy = getStrategyByKey(templateKey);
				this.lastAppliedTemplate = templateKey;
			}
		},
		loadCustomStrategy() {
			// Load the original strategy from organization settings if available
			if (this.originalPlanStrategy) {
				this.planStrategy = this.originalPlanStrategy;
				this.lastAppliedTemplate = '';
				this.status = 'success';
				this.statusText = `Reverted to your custom strategy.`;
			} else {
				this.clearStrategy();
			}
		},
		clearStrategy() {
			this.planStrategy = '';
			this.lastAppliedTemplate = '';
			this.status = 'info';
			this.statusText = `Strategy cleared. Please enter your custom strategy or select a template.`;
		},
		getStrategyName(templateKey) {
			const option = this.strategyOptions.find(opt => opt.value === (templateKey || this.lastAppliedTemplate));
			return option ? option.label : 'Custom Strategy';
		},
		getStrategyDescription(templateKey) {
			const option = this.strategyOptions.find(opt => opt.value === (templateKey || this.lastAppliedTemplate));
			return option ? option.description : '';
		},
		currentStrategyIsTemplate(templateKey) {
			return this.lastAppliedTemplate === templateKey && this.planStrategy === getStrategyByKey(templateKey);
		},
		currentStrategyIsAnyTemplate() {
			return !!this.lastAppliedTemplate && this.planStrategy === getStrategyByKey(this.lastAppliedTemplate);
		},
		handleStatusUpdate(status, message) {
			this.status = status;
			this.statusText = message;
		},
		async saveStoreSettings() {
			console.log('Saving store settings...');
			try {
				this.status = 'info';
				this.statusText = 'Saving settings...';

				// Handle email branding
				let emailBrandingToSave;
				if (typeof this.emailBranding === 'object') {
					emailBrandingToSave = JSON.stringify(this.emailBranding);
				} else {
					emailBrandingToSave = this.emailBranding;
				}

				// Prepare all settings
				const settings = {
					hasSubscriptions: String(this.hasSubscriptions),
					customerProblems: String(this.customerProblems || ''),
					influencerCollaboration: String(this.influencerCollaboration || ''),
					emailTemplates: JSON.stringify(this.emailTemplates || []),
					hasCustomComponents: String(this.hasCustomComponents),
					customImageCategories: JSON.stringify(this.customImageCategories || []),
				};

				Object.assign(settings, {
					bestSellers: String(this.bestSellers || ''),
					productCategories: String(this.productCategories || ''),
					planStrategy: String(this.planStrategy || ''),
					lastAppliedTemplate: String(this.lastAppliedTemplate || ''),
					emailBranding: emailBrandingToSave,
					hasSMS: String(this.hasSMS),
					businessGoals: String(this.businessGoals || ''),
					hasEmailGeneration: String(this.hasEmailGeneration),
					brandCustomInstructions: String(this.brandCustomInstructions || ''),
					customFonts: JSON.stringify(this.customFonts || []),
				});

				// Update all settings in sequence
				for (const [key, value] of Object.entries(settings)) {
					console.log(`Saving setting ${key}:`, value); // Debug log
					await OrganizationSettings.updateOrganizationSetting(key, value);
				}

				// Parse and clean the domain before saving
				const cleanedDomain = this.parseDomainForSaving(this.storeDomain);

				// Update organization details
				await OrgServices.patchOrgById(this.currentOrg.id, {
					name: this.storeName,
					externalDomain: cleanedDomain,
					description: this.storeDescription || '',
					sampleLanguage: this.exampleLanguage || '',
				});

				// Update localStorage for immediate use by integrations
				if (cleanedDomain) {
					localStorage.setItem('externalDomain', cleanedDomain);
				}

				this.status = 'success';
				this.statusText = 'Store AI settings saved successfully';
			} catch (error) {
				console.error('Error saving settings:', error);
				this.status = 'error';
				this.statusText = 'Failed to save store AI settings: ' + (error.message || 'Unknown error');
			}
		},
		// New Font Management Methods
		handleSingleFontUpload(event) {
			console.log('handleSingleFontUpload called with event:', event);
			const file = event.target.files[0];
			console.log('Selected file:', file);
			if (!file) return;

			// Validate file type
			const allowedTypes = ['.ttf', '.otf', '.woff', '.woff2'];
			const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'));
			if (!allowedTypes.includes(fileExtension)) {
				this.status = 'error';
				this.statusText = `Invalid file type. Please upload a font file (${allowedTypes.join(', ')})`;
				event.target.value = '';
				return;
			}

			// Validate file size (max 5MB for fonts)
			const maxSize = 5 * 1024 * 1024; // 5MB
			if (file.size > maxSize) {
				this.status = 'error';
				this.statusText = 'Font file too large. Maximum size is 5MB.';
				event.target.value = '';
				return;
			}

			this.selectedFontFile = file;
			console.log('Font file selected successfully:', file.name, 'Size:', Math.round(file.size / 1024), 'KB');
			this.status = 'success';
			this.statusText = `Selected: ${file.name} (${Math.round(file.size / 1024)} KB)`;
		},
		clearSelectedFile() {
			this.selectedFontFile = null;
			if (this.$refs.singleFontInput) {
				this.$refs.singleFontInput.value = '';
			}
			this.status = '';
			this.statusText = '';
		},
		async uploadNewFont() {
			console.log('uploadNewFont called, canUploadFont:', this.canUploadFont);
			console.log('newFontFamily:', this.newFontFamily);
			console.log('newFontWeight:', this.newFontWeight);
			console.log('selectedFontFile:', this.selectedFontFile);

			if (!this.canUploadFont) {
				console.log('Cannot upload font - validation failed');
				return;
			}

			try {
				this.isProcessingFonts = true;
				this.status = 'info';
				this.statusText = `Uploading ${this.selectedFontFile.name}...`;

				// Create FormData for upload
				const formData = new FormData();
				formData.append('file', this.selectedFontFile);
				formData.append('weight', this.newFontWeight.toString());
				formData.append('fontFamily', this.newFontFamily);

				// Upload to S3
				const response = await fetch(`${URL_DOMAIN}/branding/fonts/upload`, {
					method: 'POST',
					headers: {
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: formData
				});

				if (!response.ok) {
					throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
				}

				const uploadResult = await response.json();

				// Find or create font family
				let fontFamily = this.customFonts.find(f => f.name === this.newFontFamily);
				if (!fontFamily) {
					fontFamily = {
						name: this.newFontFamily,
						cssValue: this.newFontFamily,
						cssUrl: '',
						weights: []
					};
					this.customFonts.push(fontFamily);
				}

				// Add weight to font family
				const weightLabel = this.fontWeightOptions.find(w => w.value === this.newFontWeight)?.label || 'Custom';
				fontFamily.weights.push({
					name: weightLabel,
					weight: this.newFontWeight,
					url: uploadResult.url,
					filename: uploadResult.filename
				});

				// Generate CSS for this font family
				await this.generateFontFamilyCSS(fontFamily);

				this.status = 'success';
				this.statusText = `Added ${weightLabel} weight to ${this.newFontFamily}`;

				// Reset form
				this.newFontFamily = '';
				this.newFontWeight = 400;
				this.selectedFontFile = null;
				if (this.$refs.singleFontInput) {
					this.$refs.singleFontInput.value = '';
				}

				// Save to organization settings
				await this.saveCustomFonts();

			} catch (error) {
				console.error('Error uploading font:', error);
				this.status = 'error';
				this.statusText = `Failed to upload font: ${error.message}`;
			} finally {
				this.isProcessingFonts = false;
			}
		},
		async generateFontFamilyCSS(fontFamily) {
			try {
				// Prepare font data for CSS generation
				const fontData = {
					fontFamily: fontFamily.name,
					fonts: {}
				};

				// Convert weights to object keyed by weight
				fontFamily.weights.forEach(weight => {
					fontData.fonts[weight.weight] = weight.url;
				});

				// Call backend to generate CSS
				const response = await fetch(`${URL_DOMAIN}/branding/fonts/generate-css`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
						'Authorization': `Bearer ${localStorage.getItem('token')}`,
					},
					body: JSON.stringify(fontData)
				});

				if (!response.ok) {
					throw new Error(`CSS generation failed: ${response.status} ${response.statusText}`);
				}

				const result = await response.json();
				fontFamily.cssUrl = result.cssUrl;

				// Load the CSS for preview
				this.loadFontCSS(fontFamily.cssUrl);

			} catch (error) {
				console.error('Error generating CSS for font family:', error);
				throw error;
			}
		},
		loadFontCSS(cssUrl) {
			// Remove existing font stylesheets for this font
			const existingLinks = document.querySelectorAll(`link[data-font-css="${cssUrl}"]`);
			existingLinks.forEach(link => link.remove());

			// Add new font stylesheet
			const link = document.createElement('link');
			link.rel = 'stylesheet';
			link.href = cssUrl;
			link.setAttribute('data-font-css', cssUrl);
			document.head.appendChild(link);
		},
		removeCustomFont(index) {
			const fontFamily = this.customFonts[index];
			this.customFonts.splice(index, 1);

			// Remove CSS
			if (fontFamily.cssUrl) {
				const links = document.querySelectorAll(`link[data-font-css="${fontFamily.cssUrl}"]`);
				links.forEach(link => link.remove());
			}

			this.status = 'info';
			this.statusText = `Removed font family: ${fontFamily.name}`;

			// Save changes
			this.saveCustomFonts();
		},
		async saveCustomFonts() {
			try {
				// Save the custom fonts array to organization settings
				await OrganizationSettings.updateOrganizationSetting('customFonts', JSON.stringify(this.customFonts));
			} catch (error) {
				console.error('Error saving custom fonts:', error);
			}
		},
		handleFileUpload(event) {
			const files = Array.from(event.target.files);

			files.forEach((file) => {
				const reader = new FileReader();

				reader.onload = (e) => {
					const content = e.target.result;
					this.emailTemplates.push({
						name: file.name,
						content: content,
						file: file,
					});
				};

				reader.readAsText(file);
			});
		},
		removeTemplate(index) {
			this.emailTemplates.splice(index, 1);
		},
		handleImagesLoaded(images) {
			if (!images) return;

			// Count images that have no category (excluded from AI)
			this.unspecifiedImageCount = images.filter(img =>
				(!img.imageType) ||
				img.imageType === 'Excluded' ||
				img.contentType === 'Excluded'
			).length;
		},
		// Methods for custom image categories
		addCategory() {
			const categoryName = this.newCategoryName.trim();
			if (!categoryName) return;

			// Check if category already exists in default or custom categories
			if (this.defaultImageCategories.includes(categoryName) ||
				this.customImageCategories.includes(categoryName)) {
				this.status = 'error';
				this.statusText = `Category "${categoryName}" already exists`;
				return;
			}

			this.customImageCategories.push(categoryName);
			this.newCategoryName = ''; // Clear input
		},
		removeCategory(index) {
			this.customImageCategories.splice(index, 1);
			if (this.editingCategoryIndex === index) {
				this.editingCategoryIndex = -1;
			}
		},
		startEditingCategory(index) {
			this.editingCategoryIndex = index;
			// Focus the input after the DOM updates
			this.$nextTick(() => {
				if (this.$refs.editCategoryInput && this.$refs.editCategoryInput[0]) {
					this.$refs.editCategoryInput[0].focus();
				}
			});
		},
		updateCustomCategories(categories) {
			this.customImageCategories = [...categories];
			// Save changes immediately
			this.saveStoreSettings();
			this.status = 'success';
			this.statusText = 'AI Tags updated successfully';
		},
		loadTagValues() {
			if (!this.isRaleonAdmin) {
				console.log('Not a Raleon admin, skipping tag values load');
				return;
			}

			this.tagValuesLoading = true;

			// Add skipCache parameter for localhost to avoid slow memcache
			const isLocalhost = URL_DOMAIN.includes('localhost') || URL_DOMAIN.includes('127.0.0.1');
			const url = `${URL_DOMAIN}/prompt-tags${isLocalhost ? '?skipCache=true' : ''}`;
			const token = localStorage.getItem('token');

			// Create AbortController for timeout
			const controller = new AbortController();
			// Use longer timeout for local development (memcache can be slow)
			const timeoutMs = URL_DOMAIN.includes('localhost') || URL_DOMAIN.includes('127.0.0.1') ? 300000 : 120000; // 5 minutes local, 2 minutes production
			const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

			fetch(url, {
				headers: {
					'Authorization': `Bearer ${token}`
				},
				signal: controller.signal
			})
				.then(res => {
					if (!res.ok) {
						throw new Error(`HTTP ${res.status}: ${res.statusText}`);
					}
					return res.json();
				})
				.then(data => {
					clearTimeout(timeoutId);

					console.log('Raw API response:', data);

					// The API returns a Record<string, string> where key is tag name and value is tag value
					const tagValues = Object.entries(data || {}).map(([tag, value]) => {
						const isEmpty = !value || value.trim() === '';
						console.log(`Tag: ${tag}, Value length: ${value ? value.length : 0}, isEmpty: ${isEmpty}`);
						return {
							tag,
							value: value || '',
							isEmpty
						};
					});

					console.log('Processed tagValues:', tagValues);
					this.tagValues = tagValues;
					this.tagValuesLoading = false;

					if (tagValues.length === 0) {
						this.status = 'info';
						this.statusText = 'No tag values returned from API';
					} else {
						this.status = 'success';
						this.statusText = `Loaded ${tagValues.length} tag values successfully`;
					}
				})
				.catch(err => {
					clearTimeout(timeoutId);
					console.error('Failed to load tag values', err);
					this.tagValuesLoading = false;

					if (err.name === 'AbortError') {
						const timeoutSeconds = Math.round(timeoutMs / 1000);
						this.status = 'error';
						this.statusText = `Request timed out after ${timeoutSeconds} seconds. Tag processing takes time due to data gathering.`;
					} else {
						this.status = 'error';
						this.statusText = 'Failed to load tag values: ' + (err.message || 'Unknown error');
					}
				});
		},
		handleBrandingReset() {
			this.status = 'success';
			this.statusText = 'Email branding reset to defaults. Remember to click "Save Knowledge" to save your changes.';
		},
		handleBrandingUpdate(newValue) {
			console.log("Email branding updated in parent. Type:", typeof newValue, "Value:", newValue);

			// Check if we actually have a value to update
			if (!newValue) return;

			// Compare objects properly
			const currentJSON = typeof this.emailBranding === 'object' ?
				JSON.stringify(this.emailBranding) : this.emailBranding;
			const newJSON = typeof newValue === 'object' ?
				JSON.stringify(newValue) : newValue;

			// Only update if there's a real change to avoid infinite loops
			if (currentJSON !== newJSON) {
				console.log("Updating emailBranding with new value");
				this.emailBranding = newValue;

				// Show a status message to confirm changes are ready to be saved
				this.status = 'info';
				this.statusText = 'Email branding updated. Remember to click "Save Knowledge" to save changes.';
			}
		},
		// Brand Memory Management Methods
		async loadBrandMemories() {
			this.memoriesLoading = true;
			try {
				const memoriesString = await OrganizationSettings.getOrganizationSetting('brandMemories');
				if (memoriesString) {
					this.brandMemories = JSON.parse(memoriesString);
				} else {
					this.brandMemories = [];
				}
				this.status = 'success';
				this.statusText = `Loaded ${this.brandMemories.length} brand memories`;
			} catch (error) {
				console.error('Error loading brand memories:', error);
				this.status = 'error';
				this.statusText = 'Failed to load brand memories';
				this.brandMemories = [];
			} finally {
				this.memoriesLoading = false;
			}
		},
		editMemory(index) {
			this.editingMemoryIndex = index;
			this.currentMemory = { ...this.brandMemories[index] };
		},
		deleteMemory(index) {
			this.memoryToDelete = index;
			this.showDeleteConfirm = true;
		},
		confirmDeleteMemory() {
			if (this.memoryToDelete !== -1) {
				this.brandMemories.splice(this.memoryToDelete, 1);
				this.saveBrandMemories();
				this.status = 'success';
				this.statusText = 'Memory deleted successfully';
				this.memoryToDelete = -1;
			}
		},
		closeMemoryModal() {
			this.showAddMemoryModal = false;
			this.editingMemoryIndex = -1;
			this.currentMemory = {
				type: 'General',
				content: '',
				timestamp: null
			};
		},
		async saveMemory() {
			if (!this.currentMemory.content.trim()) {
				this.status = 'error';
				this.statusText = 'Memory content cannot be empty';
				return;
			}

			this.currentMemory.timestamp = new Date().toISOString();

			if (this.editingMemoryIndex !== -1) {
				// Update existing memory
				this.brandMemories[this.editingMemoryIndex] = { ...this.currentMemory };
				this.status = 'success';
				this.statusText = 'Memory updated successfully';
			} else {
				// Add new memory
				this.brandMemories.push({ ...this.currentMemory });
				this.status = 'success';
				this.statusText = 'Memory added successfully';
			}

			await this.saveBrandMemories();
			this.closeMemoryModal();
		},
		async saveBrandMemories() {
			try {
				const memoriesString = JSON.stringify(this.brandMemories);
				await OrganizationSettings.updateOrganizationSetting('brandMemories', memoriesString);
			} catch (error) {
				console.error('Error saving brand memories:', error);
				this.status = 'error';
				this.statusText = 'Failed to save brand memories';
			}
		},
		formatDate(dateString) {
			if (!dateString) return 'Unknown date';
			try {
				const date = new Date(dateString);
				return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
			} catch (error) {
				return 'Invalid date';
			}
		}
	},
	watch: {
		activeTab(newTab) {
			if (newTab === 'emailBranding') {
				// Force a refresh of the emailBranding component when the tab is selected
				this.emailBrandingKey++;
				console.log("Tab changed to emailBranding, forcing refresh with key:", this.emailBrandingKey);
			} else if (newTab === 'tags') {
				this.loadTagValues();
			} else if (newTab === 'memory') {
				this.loadBrandMemories();
			}
		}
	},
	computed: {
		customComponentCount() {
			// This will be calculated by the KnowledgeEmailComponents component
			// We're just returning 0 here since we don't need to show the count anymore
			return 0;
		},
		totalTagValues() {
			return Array.isArray(this.tagValues) ? this.tagValues : [];
		},
		filteredTagValues() {
			const tagValues = Array.isArray(this.tagValues) ? this.tagValues : [];
			console.log('Computing filteredTagValues. showEmptyTags:', this.showEmptyTags);
			console.log('Total tagValues:', tagValues.length);

			if (this.showEmptyTags) {
				console.log('Showing all tags (including empty)');
				return tagValues;
			} else {
				const filtered = tagValues.filter(item => item && !item.isEmpty);
				console.log('Filtered to non-empty tags:', filtered.length);
				console.log('Filtered tags:', filtered.map(t => `${t.tag}: ${t.isEmpty ? 'EMPTY' : 'HAS_VALUE'}`));
				return filtered;
			}
		},
		emptyTagCount() {
			const tagValues = Array.isArray(this.tagValues) ? this.tagValues : [];
			return tagValues.filter(item => item && item.isEmpty).length;
		},
		// Font computed properties
		canUploadFont() {
			return !!(this.newFontFamily.trim() && this.newFontWeight && this.selectedFontFile);
		}
	}
};
</script>

<style scoped>
/* Add any specific styles for the component overrides section */
pre {
	white-space: pre-wrap;
	word-break: break-word;
	max-height: 300px;
	overflow-y: auto;
}
</style>
