<template>
  <div class="h-full flex">
    <!-- Left side: Component List -->
    <div class="w-96 border-r bg-gray-50 overflow-y-auto">
      <!-- Header -->
      <div class="p-4 bg-white border-b sticky top-0 z-10">
        <h2 class="text-lg font-semibold mb-2">Email Section Preview</h2>
        <p class="text-sm text-gray-600 mb-4">Select a section to preview how it looks in emails</p>

        <!-- Toggle for inactive components -->
        <div class="flex items-center">
          <label class="text-sm font-medium flex items-center">
            <input
              type="checkbox"
              v-model="showInactive"
              class="mr-2 h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
            />
            Show inactive components
          </label>
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="isLoadingComponents" class="flex justify-center py-8">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
      </div>

      <!-- Component List -->
      <div v-else-if="filteredComponents.length > 0" class="divide-y divide-gray-200">
        <div
          v-for="component in filteredComponents"
          :key="component.id"
          @click="selectComponent(component)"
          :class="[
            'p-4 cursor-pointer transition-colors hover:bg-white',
            selectedComponent?.id === component.id ? 'bg-white border-l-4 border-purple-600' : ''
          ]"
        >
          <div class="flex justify-between items-start mb-1">
            <h3 class="font-medium text-sm">{{ component.name }}</h3>
            <span
              :class="[
                'text-xs px-2 py-0.5 rounded',
                getComponentTypeBadgeClass(component)
              ]"
            >
              {{ getComponentTypeLabel(component) }}
            </span>
          </div>

          <!-- AI Editable Fields indicator -->
          <div v-if="component.editableFields && Object.keys(getComponentEditableFields(component)).length > 0"
               class="flex items-center text-xs text-green-600 mt-1">
            <svg class="w-3.5 h-3.5 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
            </svg>
            {{ Object.keys(getComponentEditableFields(component)).length }} AI-editable fields
          </div>

          <!-- Status -->
          <div v-if="component.active === false" class="text-xs text-red-600 mt-1">
            Inactive
          </div>
        </div>
      </div>

      <!-- Empty state -->
      <div v-else class="p-8 text-center text-gray-500">
        <p>No components found</p>
      </div>
    </div>

    <!-- Right side: Preview Panel -->
    <div class="flex-1 bg-white overflow-hidden flex flex-col">
      <!-- Preview Header -->
      <div class="p-4 border-b">
        <div v-if="selectedComponent" class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-medium">{{ selectedComponent.name }}</h3>
            <p v-if="selectedComponent.description" class="text-sm text-gray-600 mt-1">
              AI Instructions: {{ selectedComponent.description }}
            </p>
          </div>
          <div class="flex items-center space-x-2">
            <span
              :class="[
                'text-xs px-2 py-1 rounded',
                getComponentTypeBadgeClass(selectedComponent)
              ]"
            >
              {{ getComponentTypeLabel(selectedComponent) }}
            </span>
            <span v-if="selectedComponent.active === false" class="text-xs px-2 py-1 bg-red-100 text-red-800 rounded">
              Inactive
            </span>
          </div>
        </div>
        <div v-else class="text-gray-500">
          Select a component to preview
        </div>
      </div>

      <!-- Preview Content -->
      <div class="flex-1 overflow-hidden">
        <!-- Loading state -->
        <div v-if="isLoadingPreview" class="h-full flex items-center justify-center">
          <div class="text-center">
            <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading preview...</p>
          </div>
        </div>

        <!-- Error state -->
        <div v-else-if="previewError" class="h-full flex items-center justify-center">
          <div class="text-center text-red-600 max-w-md">
            <svg class="w-12 h-12 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <p class="font-medium mb-2">Error loading preview</p>
            <p class="text-sm">{{ previewError }}</p>
          </div>
        </div>

        <!-- Email Preview -->
        <div v-else-if="emailHtml && selectedComponent" class="h-full bg-gray-100">
          <div class="h-full p-4">
            <div class="max-w-4xl mx-auto h-full bg-white rounded-lg shadow-lg overflow-hidden">
              <!-- Email client header mockup -->
              <div class="bg-gray-50 border-b px-4 py-3">
                <div class="text-xs text-gray-600 mb-1">From: Your Brand &lt;<EMAIL>&gt;</div>
                <div class="text-xs text-gray-600 mb-1">To: <EMAIL></div>
                <div class="text-sm font-medium">Preview: {{ selectedComponent.name }}</div>
              </div>
              <!-- Email content -->
              <iframe
                :srcdoc="emailHtml"
                class="w-full h-full border-0"
                style="height: calc(100% - 80px);"
              ></iframe>
            </div>
          </div>
        </div>

        <!-- Empty state -->
        <div v-else class="h-full flex items-center justify-center text-gray-500">
          <div class="text-center">
            <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"></path>
            </svg>
            <p>Select a component to see how it looks in emails</p>
          </div>
        </div>
      </div>

      <!-- Hidden Unlayer Editor for HTML generation -->
      <div style="position: absolute; left: -9999px; top: -9999px; height: 1px; width: 1px; overflow: hidden;">
        <EmailEditor
          ref="hiddenEmailEditor"
          :project-id="'267562'"
          :options="editorOptions"
          style="height: 1px; width: 1px; border: none;"
          @load="onEditorLoaded"
        />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onUnmounted, watch } from '@vue/runtime-core';
import { EmailEditor } from 'vue-email-editor';
import * as Utils from '../../client-old/utils/Utils';
import * as OrganizationSettings from '../services/organization-settings';

const URL_DOMAIN = Utils.URL_DOMAIN;

interface EditableFields {
  [key: string]: string;
}

interface CustomFont {
  label: string;
  value: string;
  url?: string;
  weights?: Array<{
    label: string;
    value: number;
  }>;
}

interface EmailComponent {
  id: number;
  name: string;
  description?: string;
  json: string;
  type: string;
  editableFields?: string | EditableFields;
  active?: boolean;
  orgId?: number | null;
  overrideId?: number | null;
}

export default defineComponent({
  name: 'KnowledgePreviewComponents',
  components: {
    EmailEditor
  },
  props: {
    orgId: {
      type: Number,
      required: true
    }
  },
  setup(props) {
    // State
    const isLoadingComponents = ref(false);
    const isLoadingPreview = ref(false);
    const components = ref<EmailComponent[]>([]);
    const selectedComponent = ref<EmailComponent | null>(null);
    const showInactive = ref(false);
    const emailHtml = ref('');
    const previewError = ref('');
    const editorReady = ref(false);

    // Editor options (reactive)
    const editorOptions = ref({
      displayMode: 'email',
      features: {
        preview: false,
        imageEditor: false,
        undoRedo: false,
        stockImages: false,
      },
      tools: {
        image: {enabled: false},
        button: {enabled: false},
        divider: {enabled: false},
        text: {enabled: false},
        html: {enabled: false},
        menu: {enabled: false},
        social: {enabled: false},
        timer: {enabled: false},
        video: {enabled: false},
        form: {enabled: false},
        row: {enabled: true}
      },
      fonts: {
        showDefaultFonts: true,
        customFonts: [] as CustomFont[]
      }
    });

    // Computed
    const filteredComponents = computed(() => {
      return components.value.filter(comp => {
        const isActive = comp.active === undefined || comp.active === true;
        return showInactive.value || isActive;
      });
    });

    // Methods
    const loadFontSettings = async () => {
      try {
        // Load custom font settings from organization settings
        const customFontUrl = await OrganizationSettings.getOrganizationSetting('customFontUrl') || '';
        const customFontName = await OrganizationSettings.getOrganizationSetting('customFontName') || '';
        const customFontValue = await OrganizationSettings.getOrganizationSetting('customFontValue') || '';
        
        console.log('Loaded font settings for preview:', {
          url: customFontUrl,
          name: customFontName,
          value: customFontValue
        });
        
        // Update editor options with custom font
        const customFonts: CustomFont[] = [];
        
        if (customFontUrl && customFontName && customFontValue) {
          const customFont: CustomFont = {
            label: customFontName,
            value: customFontValue,
            url: customFontUrl,
            weights: [
              { label: 'Regular', value: 400 },
              { label: 'Bold', value: 700 }
            ]
          };
          
          customFonts.push(customFont);
          console.log('Added custom font to preview editor:', customFont);
        }
        
        // Update the editor options
        editorOptions.value = {
          ...editorOptions.value,
          fonts: {
            showDefaultFonts: true,
            customFonts: customFonts
          }
        };
        
        console.log('Updated preview editor font options:', editorOptions.value.fonts);
      } catch (error) {
        console.error('Error loading font settings for preview:', error);
      }
    };

    const loadComponents = async () => {
      isLoadingComponents.value = true;
      try {
        const url = new URL(`${URL_DOMAIN}/unlayer-components`);

        // Filter to get both global components and organization-specific components
        const filter = {
          where: {
            or: [
              { orgId: null }, // Global components
              { orgId: props.orgId } // Organization-specific components
            ]
          }
        };

        url.searchParams.append('filter', JSON.stringify(filter));

        const response = await fetch(url.toString(), {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('token')}`
          }
        });

        if (!response.ok) {
          throw new Error(`HTTP error ${response.status}`);
        }

        const data = await response.json();

        if (data && Array.isArray(data)) {
          components.value = data.map(comp => {
            // Ensure editableFields is properly formatted
            if (comp.editableFields && typeof comp.editableFields === 'string') {
              try {
                comp.editableFields = JSON.parse(comp.editableFields);
              } catch (e) {
                console.error('Error parsing component editable fields:', e);
                comp.editableFields = {};
              }
            } else if (!comp.editableFields) {
              comp.editableFields = {};
            }
            return comp;
          });
        }
      } catch (error) {
        console.error('Failed to load email components:', error);
        previewError.value = 'Failed to load email components';
      } finally {
        isLoadingComponents.value = false;
      }
    };

    const getComponentEditableFields = (component: EmailComponent): EditableFields => {
      if (!component.editableFields) return {};

      if (typeof component.editableFields === 'string') {
        try {
          return JSON.parse(component.editableFields);
        } catch (e) {
          return {};
        }
      }

      return component.editableFields as EditableFields;
    };

    const getComponentTypeLabel = (component: EmailComponent): string => {
      if (component.orgId === props.orgId && component.overrideId) {
        return 'Override';
      } else if (component.orgId === props.orgId) {
        return 'Organization';
      }
      return 'Global';
    };

    const getComponentTypeBadgeClass = (component: EmailComponent): string => {
      if (component.orgId === props.orgId && component.overrideId) {
        return 'bg-purple-100 text-purple-800';
      } else if (component.orgId === props.orgId) {
        return 'bg-blue-100 text-blue-800';
      }
      return 'bg-gray-100 text-gray-800';
    };

    const selectComponent = (component: EmailComponent) => {
      selectedComponent.value = component;
      generatePreview();
    };

    const generatePreview = async () => {
      if (!selectedComponent.value || !editorReady.value) return;

      isLoadingPreview.value = true;
      previewError.value = '';
      emailHtml.value = '';

      try {
        const editor = (hiddenEmailEditor.value as any)?.editor;
        if (!editor) {
          throw new Error('Editor not ready');
        }

        // Parse the component JSON
        let design;
        try {
          design = JSON.parse(selectedComponent.value.json);
        } catch (e) {
          throw new Error('Invalid component JSON');
        }

        // Create a simple email design with the component
        const emailDesign = {
          body: {
            rows: [
              // Add the component as a row
              design
            ],
            values: {
              backgroundColor: '#f4f4f4',
              contentWidth: '600px',
              fontFamily: {
                label: 'Arial',
                value: 'arial,helvetica,sans-serif'
              }
            }
          }
        };

        // Load the design into the editor
        await new Promise<void>((resolve, reject) => {
          editor.loadDesign(design);
          // Give it a moment to process
          setTimeout(resolve, 100);
        });

        // Export HTML
        await new Promise<void>((resolve, reject) => {
          editor.exportHtml((data: { design: any; html: string }) => {
            if (data.html) {
              emailHtml.value = data.html;
              resolve();
            } else {
              reject(new Error('No HTML generated'));
            }
          }, {
            cleanup: true
          });
        });

      } catch (error) {
        console.error('Error generating preview:', error);
        previewError.value = error instanceof Error ? error.message : 'Failed to generate preview';
      } finally {
        isLoadingPreview.value = false;
      }
    };

    // Refs
    const hiddenEmailEditor = ref();

    const onEditorLoaded = () => {
      console.log('Unlayer editor loaded for preview');
      editorReady.value = true;

      // If a component is already selected, generate its preview
      if (selectedComponent.value) {
        generatePreview();
      }
    };

    // Lifecycle
    onMounted(() => {
      loadComponents();
      loadFontSettings();
    });

    // Watch for editor ready state
    watch(editorReady, (ready) => {
      if (ready && selectedComponent.value) {
        generatePreview();
      }
    });

    return {
      // State
      isLoadingComponents,
      isLoadingPreview,
      filteredComponents,
      selectedComponent,
      showInactive,
      emailHtml,
      previewError,
      editorOptions,

      // Methods
      selectComponent,
      getComponentEditableFields,
      getComponentTypeLabel,
      getComponentTypeBadgeClass,

      // Refs
      hiddenEmailEditor,
      onEditorLoaded
    };
  }
});
</script>

<style scoped>
/* Custom scrollbar for component list */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: #f3f4f6;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Email preview styling */
iframe {
  background: white;
}
</style>
