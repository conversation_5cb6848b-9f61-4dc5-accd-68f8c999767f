import {authenticate, TokenService} from '@loopback/authentication';
import {TokenServiceBindings, User} from '@loopback/authentication-jwt';
import {authorize} from '@loopback/authorization';
import {inject, service} from '@loopback/core';
import {repository} from '@loopback/repository';
import {
	api, get, post, param, Request, RestBindings, requestBody, getModelSchemaRef, patch, HttpErrors, SchemaObject
} from '@loopback/rest';
import {SecurityBindings, UserProfile} from '@loopback/security';
import {injectUserOrgId, GuardSkipStrategy, guardStrategy, skipGuardCheck} from '../interceptors';
import {IDENTITY_TYPES, InventoryCouponWithRelations, Organization, SelfServiceAdditionalSignup, SelfServiceSignup} from '../models';

import {UserRepository, ProjectRepository, OrganizationRepository, DashboardRepository, SegmentRepository, DataConnectionsRepository, UserIdentityRepository, CurrencyRepository, SupportedCurrenciesRepository, OrganizationPlanRepository, PlanRepository, PlanFeatureRepository} from '../repositories';
import {basicAuthorization, UserManagementService, validateCredentials} from '../services';
import { AssociationTypes } from '@hubspot/api-client';
import {AccessTokenService} from '../services/oauth/token-service';
import {UserCredentialsRepository} from '../repositories/user-credentials.repository';
import {FeatureService} from '../services/feature.service';
import {DiscountCodeService} from '../services/shopify/discount-code.service';
import {OnboardingService} from '../services/onboarding.service';
import {StripeBillingService} from '../services/stripe/stripe-billing.service';

const fetch = require('node-fetch');
const AWS = require('aws-sdk');
const aws4 = require('aws4')
const hubspot = require('@hubspot/api-client');
// const urlMetadata = require('url-metadata');

const DATA_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com'
const UTM_API = 'j0v36abmdj.execute-api.us-east-1.amazonaws.com'
const AWS_ACCESS_KEY = process.env.API_ACCESS_KEY;
const AWS_SECRET_KEY = process.env.API_SECRET_KEY;

function getURL(path: string, method: string, body?: any, host?: string) {
	const opts = {
		host: host || DATA_API,
		path: path,
		region: 'us-east-1',
		service: 'execute-api',
		mode: 'cors',
		body: body != undefined ? JSON.stringify(body) : undefined,
		headers: {
			'Content-Type': 'application/json',
		},
		method: method
	}
	return aws4.sign(opts, {accessKeyId: AWS_ACCESS_KEY, secretAccessKey: AWS_SECRET_KEY});
}

export const GOAL_API = 'bcie42aco8.execute-api.us-east-1.amazonaws.com';

@api({basePath: '/api/v1'})
@guardStrategy(new GuardSkipStrategy())
export class OnboardController {
	constructor(
		@inject(RestBindings.Http.REQUEST) private req: Request,
		@repository('UserRepository') private userRepository: UserRepository,
		@repository('UserIdentityRepository') private userIdentityRepository: UserIdentityRepository,
		@repository('ProjectRepository') private projectRepository: ProjectRepository,
		@repository('OrganizationRepository') private orgRepository: OrganizationRepository,
		@service(UserManagementService) private userManagementService: UserManagementService,
		@service(AccessTokenService) private tokenService: AccessTokenService,
		@repository(DashboardRepository) private dashboardRepository: DashboardRepository,
		@repository(SegmentRepository) private segmentRepository: SegmentRepository,
		@repository(DataConnectionsRepository) private dataConnectionsRepository: DataConnectionsRepository,
		@repository(UserCredentialsRepository) private userCredentialsRepository: UserCredentialsRepository,
		@repository(CurrencyRepository) private currencyRepository: CurrencyRepository,
		@repository(SupportedCurrenciesRepository) private supportedCurrenciesRepository: SupportedCurrenciesRepository,
		@repository(OrganizationPlanRepository) private orgPlanRepository: OrganizationPlanRepository,
		@repository(PlanRepository) private planRepository: PlanRepository,
		@repository(PlanFeatureRepository) private planFeatureRepository: PlanFeatureRepository,
		@service(FeatureService) private featureService: FeatureService,
		@service(DiscountCodeService) private discountCodeService: DiscountCodeService,
		@inject(TokenServiceBindings.TOKEN_SERVICE) public jwtService: TokenService,
		@service(OnboardingService) private onboardingService: OnboardingService,
		@service(StripeBillingService) private stripeBillingService: StripeBillingService
	) { }

	@post('/onboard/event-stream-project', {
		responses: {
			'200': {
				description: 'create default event stream project',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async createEventStreamProject(
		@param.query.string('applicationFilter') applicationFilter = '',
		@param.query.string('projectName') projectName = '',
		@injectUserOrgId() orgId: number
	): Promise<object> {
		let response: any;
		let requestBody: any = { org_id: orgId };
		if (applicationFilter) requestBody.application_filter = applicationFilter;
		try {
			const url = `/dev/event-stream/register`;
			const signedRequest = getURL(url, 'POST', requestBody);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.json();

		if (response.status == 200) {
			let projectResult = await this.projectRepository.create({
				name: projectName ? projectName : 'Event Stream',
				description: 'Default project for event stream',
				organizationId: orgId,
				uuid: data.project_uuid,
				activationDate: new Date().toISOString(),
			});
		}

		return {
			status: response.status,
			data: data
		};
	}


	@post('/onboard/self-service', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async selfServiceOnboard(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							organizationName: {
								type: 'string',
							},
							email: {
								type: 'string',
							},
							password: {
								type: 'string',
							},
							createToken: {
								type: 'boolean',
							},
							externalDomain: {
								type: 'string',
							},
							currencyCode: {
								type: 'string',
							},
							plan: {
								type: 'object',
							},
							orgId: {
								type: 'number',
							},
							userId: {
								type: 'number',
							},
						},
						required: ['organizationName', 'email', 'password']
					},
				},
			},
		})
		signupData: SelfServiceSignup & TokenOption,
	) {
		let org: Organization;
		console.log('Self-service signup data:', signupData);
		if (signupData.orgId) {
			// Update existing organization
			org = await this.orgRepository.findById(signupData.orgId);
			if (!org) {
				throw new HttpErrors.NotFound('Organization not found');
			}

			// Update org properties
			org.name = signupData.organizationName;
			org.selfService = true;
			org.externalDomain = signupData.externalDomain || '';
			if (signupData.externalPlanDetails) {
				org.externalPlanDetails = signupData.externalPlanDetails;
			}
			console.log('Updating existing organization:', org);
			await this.orgRepository.updateById(org.id!, {
				selfService: org.selfService,
				externalDomain: org.externalDomain,
				externalPlanDetails: org.externalPlanDetails,
			});
			if (signupData.createToken) {
				console.log('Creating user token for existing user' + signupData.userId);
				const user = await this.userRepository.findById(signupData.userId);
				if (!user) {
					throw new HttpErrors.NotFound('User not found');
				}
				const userData = {
					id: user.id!,
					organizationId: org.id!,
				}
				const token = await this.tokenService.generateUserToken(userData);

				return {
					...user,
					organizationId: org.id,
					accessToken: token,
				};
			}
		} else {
			// Create new organization
			const orgData: any = {
				name: signupData.organizationName,
				selfService: true,
				externalDomain: signupData.externalDomain || '',
			}

			if (signupData.externalPlanDetails) {
				orgData.externalPlanDetails = signupData.externalPlanDetails;
			}
			console.log('Creating new organization:', orgData);
			org = await this.createOrganization(orgData);
		}

		if (signupData.currencyCode) {
			await this.setDefaultCurrency(signupData.currencyCode, org.id!);
		}

		const newUserRequest = {
			email: signupData.email,
			password: signupData.password!,
			firstName: signupData.firstName || signupData.email,
			lastName: signupData.lastName,
			isSecondaryAccount: false,
		};

		console.log(newUserRequest);

		if (!newUserRequest.password || newUserRequest.password.length < 8) {
			// generate random password
			newUserRequest.password = Math.random().toString(36).slice(-8);
		}

		validateCredentials(newUserRequest);

		this.createDefaultAudiences(org.id!).catch();

		const user = await this.userManagementService.createUser(newUserRequest);
		const roles = ['admin'];

		this.createHubspotEntry(org, user).catch();

		await this.updateUser(user.id!, {
			...user,
			organizationId: org.id,
			roles
		});

		if (signupData.createToken) {

			const userData = {
				id: user.id!,
				organizationId: org.id!,
			}

			const token = await this.tokenService.generateUserToken(userData);
			await this.userIdentityRepository.create({
				identityType: IDENTITY_TYPES.SELF_SERVICE,
				identityValue: token,
				userId: user.id,
			});

			return {
				...user,
				organizationId: org.id,
				accessToken: token,
			};
		}

		return {
			...user,
			organizationId: org.id,
		};
	}

	@post('/onboard/agency/self-service', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	@skipGuardCheck()
        async agencyOnboard(
                @requestBody({
                        content: {
                                'application/json': {
					schema: {
						type: 'object',
						properties: {
							organizationName: {
								type: 'string',
							},
							email: {
								type: 'string',
							},
							password: {
								type: 'string',
							},
							createToken: {
								type: 'boolean',
							},
							externalDomain: {
								type: 'string',
							},
							currencyCode: {
								type: 'string',
							},
							plan: {
								type: 'object',
							}
						},
						required: ['organizationName', 'email', 'password']
					},
				},
			},
		})
		signupData: SelfServiceSignup & TokenOption,
	) {
		const orgData: any = {
			name: signupData.organizationName,
			selfService: true,
			externalDomain: signupData.externalDomain || '',
		}

/* 		const org = await this.createOrganization(orgData);

		if (signupData.currencyCode) {
			await this.setDefaultCurrency(signupData.currencyCode, org.id!);
		}
 */
		const org = await this.orgRepository.create(orgData);

		await this.orgPlanRepository.create({
			orgId: org.id,
			planId: 15,
			status: 'ACTIVE',
		})
		// Call updateFeatureState
		await this.featureService.updateLoyaltyAndGwpEnabled(org.id!);
		// Check if a primary user with this email already exists
		const existingPrimaryUser = await this.userRepository.findOne({
			where: {
				or: [
					{ email: signupData.email, isSecondaryAccount: false },
					{ email: signupData.email, isSecondaryAccount: null as any }
				]
			},
		});

		let user: any;
		const roles = ['admin'];

		if (existingPrimaryUser) {
			// User already exists - preserve access to old org and switch primary to new org
			console.log('Existing user found, preserving old org access and switching to new org:', org.id);

			const oldOrgId = existingPrimaryUser.organizationId;

			// Create a secondary account for the OLD organization to preserve access
			const secondaryUserRequest = {
				email: signupData.email,
				firstName: existingPrimaryUser.firstName || signupData.firstName || signupData.email,
				lastName: existingPrimaryUser.lastName || signupData.lastName,
				isSecondaryAccount: true,
			};

			const secondaryUser = await this.userRepository.create(secondaryUserRequest);
			await this.updateUser(secondaryUser.id!, {
				...secondaryUser,
				organizationId: oldOrgId,
				roles: existingPrimaryUser.roles || ['admin']
			});

			// Update the PRIMARY user to point to the NEW organization
			await this.updateUser(existingPrimaryUser.id!, {
				...existingPrimaryUser,
				organizationId: org.id,
				roles,
				// Update name fields if they were provided and are currently empty
				firstName: existingPrimaryUser.firstName || signupData.firstName || signupData.email,
				lastName: existingPrimaryUser.lastName || signupData.lastName,
			});

			// Use the updated primary user
			user = await this.userRepository.findById(existingPrimaryUser.id!);
		} else {
			// No existing user - create a new one
			const newUserRequest = {
				email: signupData.email,
				password: signupData.password!,
				firstName: signupData.firstName || signupData.email,
				lastName: signupData.lastName,
				isSecondaryAccount: false,
			};

			console.log('Creating new user:', newUserRequest);

			if (!newUserRequest.password || newUserRequest.password.length < 8) {
				// generate random password
				newUserRequest.password = Math.random().toString(36).slice(-8);
			}

			validateCredentials(newUserRequest);

			user = await this.userManagementService.createUser(newUserRequest);

			await this.updateUser(user.id!, {
				...user,
				organizationId: org.id,
				roles
			});
		}

		this.createHubspotEntry(org, user).catch();

		if (signupData.createToken) {

			const userData = {
				id: user.id!,
				organizationId: org.id!,
			}

			const token = await this.tokenService.generateUserToken(userData);
			await this.userIdentityRepository.create({
				identityType: IDENTITY_TYPES.SELF_SERVICE,
				identityValue: token,
				userId: user.id,
			});

			return {
				...user,
				organizationId: org.id,
                        accessToken: token,
                        };
                }

                return {
                        ...user,
                        organizationId: org.id,
                };
        }

        @post('/onboard/google-self-service', {
                responses: {
                        '200': {
                                description: 'User',
                                content: {
                                        'application/json': {
                                                schema: {
                                                        'x-ts-type': User,
                                                },
                                        },
                                },
                        },
                },
        })
        @skipGuardCheck()
        async googleSelfServiceOnboard(
                @requestBody({
                        content: {
                                'application/json': {
                                        schema: {
                                                type: 'object',
                                                properties: {
                                                        organizationName: {type: 'string'},
                                                        externalDomain: {type: 'string'},
                                                        createToken: {type: 'boolean'},
                                                        idToken: {type: 'string'},
                                                },
                                                required: ['organizationName', 'idToken']
                                        },
                                },
                        },
                }) signupData: SelfServiceSignup & {idToken: string} & TokenOption,
        ) {
                const payload = await this.userManagementService.verifyGoogleToken(signupData.idToken);
                if (!payload.email) {
                        throw new HttpErrors.Unauthorized('Invalid Google token');
                }

                const orgData: any = {
                        name: signupData.organizationName,
                        selfService: true,
                        externalDomain: signupData.externalDomain || '',
                };
                const org = await this.orgRepository.create(orgData);
                await this.orgPlanRepository.create({
                        orgId: org.id,
                        planId: 15,
                        status: 'ACTIVE',
                });
                await this.featureService.updateLoyaltyAndGwpEnabled(org.id!);

                // Check if a primary user with this email already exists
                const existingPrimaryUser = await this.userRepository.findOne({
                        where: {
                                or: [
                                        { email: payload.email, isSecondaryAccount: false },
                                        { email: payload.email, isSecondaryAccount: null as any }
                                ]
                        },
                });

                let user: any;
                const roles = ['admin'];

                if (existingPrimaryUser) {
                        // User already exists - preserve access to old org and switch primary to new org
                        console.log('Existing Google user found, preserving old org access and switching to new org:', org.id);

                        const oldOrgId = existingPrimaryUser.organizationId;

                        // Create a secondary account for the OLD organization to preserve access
                        const secondaryUserRequest = {
                                email: payload.email,
                                firstName: existingPrimaryUser.firstName || payload.given_name || payload.name || payload.email,
                                lastName: existingPrimaryUser.lastName || payload.family_name,
                                isSecondaryAccount: true,
                        };

                        const secondaryUser = await this.userRepository.create(secondaryUserRequest);
                        await this.updateUser(secondaryUser.id!, {
                                ...secondaryUser,
                                organizationId: oldOrgId,
                                roles: existingPrimaryUser.roles || ['admin']
                        });

                        // Update the PRIMARY user to point to the NEW organization
                        await this.updateUser(existingPrimaryUser.id!, {
                                ...existingPrimaryUser,
                                organizationId: org.id,
                                roles,
                                // Update name fields if they were provided and are currently empty
                                firstName: existingPrimaryUser.firstName || payload.given_name || payload.name || payload.email,
                                lastName: existingPrimaryUser.lastName || payload.family_name,
                        });

                        // Use the updated primary user
                        user = await this.userRepository.findById(existingPrimaryUser.id!);
                } else {
                        // No existing user - create a new one
                        const newUserRequest = {
                                email: payload.email,
                                password: Math.random().toString(36).slice(-8),
                                firstName: payload.given_name || payload.name || payload.email,
                                lastName: payload.family_name,
                                isSecondaryAccount: false,
                        };

                        validateCredentials(newUserRequest);

                        user = await this.userManagementService.createUser(newUserRequest);

                        await this.updateUser(user.id!, {
                                ...user,
                                organizationId: org.id,
                                roles,
                        });
                }

                this.createHubspotEntry(org, user).catch();

                if (signupData.createToken) {
                        const userData = {
                                id: user.id!,
                                organizationId: org.id!,
                        };

                        const token = await this.tokenService.generateUserToken(userData);
                        await this.userIdentityRepository.create({
                                identityType: IDENTITY_TYPES.GOOGLE,
                                identityValue: payload.sub || signupData.idToken,
                                userId: user.id,
                        });

                        return {
                                ...user,
                                organizationId: org.id,
                                accessToken: token,
                        };
                }

                return {
                        ...user,
                        organizationId: org.id,
                };
        }

	@authenticate('jwt')
	@authorize({
		voters: [basicAuthorization],
	})
	@post('/onboard/self-service-additional', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	@skipGuardCheck()
	async selfServiceOnboardAdditionalOrg(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							organizationName: {
								type: 'string',
							},
							createToken: {
								type: 'boolean',
							},
							externalDomain: {
								type: 'string',
							}
						},
						required: ['organizationName', 'email', 'password']
					},
				},
			},
		})
		signupData: SelfServiceAdditionalSignup & TokenOption,

		@inject(SecurityBindings.USER)
		currentUserProfile: UserProfile,
	) {
		const existingOrgUser = await this.userRepository.findById(currentUserProfile.id);

		// Find the parent agency organization (the user's current organization)
		const parentOrgId = existingOrgUser.organizationId;
		if (!parentOrgId) {
			throw new HttpErrors.BadRequest('User must belong to an organization to create additional brands');
		}

		// Check if the parent organization is an agency
		const parentOrg = await this.orgRepository.findById(parentOrgId);
		const isAgencyOrg = parentOrg.orgType === 'agency';

		const org = {
			name: signupData.organizationName,
			selfService: true,
			externalDomain: signupData.externalDomain || '',
			parentOrgId: parentOrgId, // Set the parent organization
		};
		const newOrg = await this.orgRepository.create(org);

		// If parent org is an agency, put new org on plan 17 without subscription ID
		// Otherwise, use the default plan 15
		const planId = isAgencyOrg ? 17 : 15;

		await this.orgPlanRepository.create({
			orgId: newOrg.id,
			planId: planId,
			status: 'ACTIVE',
			// No subscriptionId for agency sub-brands
		})
		// Call updateFeatureState
		await this.featureService.updateLoyaltyAndGwpEnabled(newOrg.id!);

		// Update parent agency subscription pricing based on brand count
		await this.updateAgencySubscriptionPricing(parentOrgId);

		await this.onboardingService.onboardBegin({
			externalDomain: signupData.externalDomain
		}, newOrg.id!);

		// Copy all users from the agency organization to the new brand organization
		await this.copyUsersFromAgencyToNewOrg(parentOrgId, newOrg.id!);

		// Copy organization settings from the agency organization to the new brand organization
		await this.copyOrganizationSettingsFromAgencyToNewOrg(parentOrgId, newOrg.id!);

		// Get the current user in the new organization for token generation
		const currentUserInNewOrg = await this.userRepository.findOne({
			where: {
				email: existingOrgUser.email,
				organizationId: newOrg.id
			}
		});

		if (!currentUserInNewOrg) {
			throw HttpErrors.InternalServerError('Failed to create user in new organization');
		}

		this.createHubspotEntry(newOrg, currentUserInNewOrg).catch();

		if (signupData.createToken) {

			const userData = {
				id: currentUserInNewOrg.id!,
				organizationId: newOrg.id!,
			}

			const token = await this.tokenService.generateUserToken(userData);
			await this.userIdentityRepository.create({
				identityType: IDENTITY_TYPES.SELF_SERVICE,
				identityValue: token,
				userId: currentUserInNewOrg.id,
			});

			return {
				...currentUserInNewOrg,
				organizationId: newOrg.id,
				accessToken: token,
			};
		}


		const userProfile = this.userManagementService.convertToUserProfile(currentUserInNewOrg);
		const token = await this.jwtService.generateToken(userProfile);

		return {
			...currentUserInNewOrg,
			organizationId: newOrg.id,
			token
		};
	}

	/**
	 * Updates the agency's subscription pricing based on the number of brands
	 * @param agencyOrgId - The ID of the agency organization
	 */
	private async updateAgencySubscriptionPricing(agencyOrgId: number): Promise<void> {
		try {
			// Get the agency organization and its plan
			const agencyOrg = await this.orgRepository.findById(agencyOrgId);
			const agencyPlan = await this.orgPlanRepository.findOne({
				where: { orgId: agencyOrgId, status: 'ACTIVE' },
				include: [{ relation: 'plan' }]
			});

			if (!agencyPlan || !agencyPlan.plan) {
				console.warn(`No active plan found for agency organization ${agencyOrgId}`);
				return;
			}

			// Get the brands-included limit for the agency's plan
			const brandsIncludedLimit = await this.getBrandsIncludedLimit(agencyPlan.planId);

			// Count the current number of brands (child organizations)
			const brandCount = await this.getBrandCount(agencyOrgId);

			// Calculate extra brands over the included limit
			const extraBrands = Math.max(0, brandCount - brandsIncludedLimit);

			if (extraBrands > 0) {
				// Get the Extra Agency Brand plan price ($125)
				const extraBrandPlan = await this.planRepository.findById(17); // Extra Agency Brand plan ID
				if (!extraBrandPlan) {
					console.error('Extra Agency Brand plan not found');
					return;
				}

				// Check if the agency has an Extra Agency Brand plan with priceOverride
				const agencyExtraBrandPlan = await this.orgPlanRepository.findOne({
					where: { orgId: agencyOrgId, planId: 17 }, // Extra Agency Brand plan ID
					include: [{ relation: 'plan' }]
				});

				// Calculate new total price: base plan price + (extra brands * extra brand price)
				const basePlanPrice = agencyPlan.plan.price;
				// Use priceOverride for extra brand price if available
				const extraBrandPrice = (agencyExtraBrandPlan && agencyExtraBrandPlan.priceOverride)
					? agencyExtraBrandPlan.priceOverride
					: extraBrandPlan.price;
				const totalPrice = basePlanPrice + (extraBrands * extraBrandPrice);

				// Update the organization plan with the new price override
				await this.orgPlanRepository.updateById(agencyPlan.id, {
					priceOverride: totalPrice
				});

				// Propagate the price change to Stripe if there's an active subscription
				if (agencyPlan.subscriptionId && agencyPlan.status === 'ACTIVE') {
					try {
						await this.stripeBillingService.migrateToPlan(
							agencyOrgId,
							agencyPlan.plan,
							totalPrice
						);
						console.log(`Updated Stripe subscription for agency ${agencyOrgId} to $${totalPrice}`);
					} catch (error) {
						console.error(`Failed to update Stripe subscription for agency ${agencyOrgId}:`, error);
						// Don't throw error to avoid breaking the onboarding flow
					}
				}

				console.log(`Updated agency ${agencyOrgId} subscription: ${brandCount} brands, ${extraBrands} extra, new price: $${totalPrice}`);
			} else {
				// Remove price override if within included limit
				const originalPrice = agencyPlan.plan.price;
				await this.orgPlanRepository.updateById(agencyPlan.id, {
					priceOverride: undefined
				});

				// Propagate the price change back to original price in Stripe if there's an active subscription
				if (agencyPlan.subscriptionId && agencyPlan.status === 'ACTIVE') {
					try {
						await this.stripeBillingService.migrateToPlan(
							agencyOrgId,
							agencyPlan.plan,
							originalPrice
						);
						console.log(`Reverted Stripe subscription for agency ${agencyOrgId} to original price $${originalPrice}`);
					} catch (error) {
						console.error(`Failed to revert Stripe subscription for agency ${agencyOrgId}:`, error);
						// Don't throw error to avoid breaking the onboarding flow
					}
				}

				console.log(`Agency ${agencyOrgId} within included brand limit: ${brandCount}/${brandsIncludedLimit} brands`);
			}
		} catch (error) {
			console.error(`Error updating agency subscription pricing for org ${agencyOrgId}:`, error);
			// Don't throw error to avoid breaking the onboarding flow
		}
	}

	/**
	 * Gets the brands-included limit for a given plan
	 * @param planId - The plan ID
	 * @returns The number of included brands
	 */
	private async getBrandsIncludedLimit(planId: number): Promise<number> {
		const planFeature = await this.planFeatureRepository.findOne({
			where: {
				planId: planId,
				featureId: 'brands-included'
			}
		});

		if (planFeature && planFeature.enabled) {
			return planFeature.unlimited ? Number.MAX_SAFE_INTEGER : (planFeature.limit || 0);
		}

		return 0; // Default to 0 if feature not found or not enabled
	}

	/**
	 * Counts the number of brands (child organizations) for an agency
	 * @param agencyOrgId - The agency organization ID
	 * @returns The number of child organizations
	 */
	private async getBrandCount(agencyOrgId: number): Promise<number> {
		const childOrgs = await this.orgRepository.find({
			where: { parentOrgId: agencyOrgId }
		});

		return childOrgs.length;
	}

	/**
	 * Public method to update agency subscription pricing when brands are added or removed
	 * This can be called from other controllers when organizations are deleted
	 * @param agencyOrgId - The agency organization ID
	 */
	async updateAgencyPricingForBrandChange(agencyOrgId: number): Promise<void> {
		await this.updateAgencySubscriptionPricing(agencyOrgId);
	}

	/**
	 * Copies all users from the agency organization to the new brand organization
	 * @param agencyOrgId - The agency organization ID
	 * @param newOrgId - The new brand organization ID
	 */
	private async copyUsersFromAgencyToNewOrg(agencyOrgId: number, newOrgId: number): Promise<void> {
		try {
			// Get all users from the agency organization
			const agencyUsers = await this.userRepository.find({
				where: {
					organizationId: agencyOrgId
				}
			});

			console.log(`Found ${agencyUsers.length} users in agency organization ${agencyOrgId} to copy to new organization ${newOrgId}`);

			// Copy each user to the new organization if they don't already exist there
			for (const agencyUser of agencyUsers) {
				// Check if user already exists in the new organization
				const existingUser = await this.userRepository.findOne({
					where: {
						email: agencyUser.email,
						organizationId: newOrgId
					}
				});

				if (!existingUser) {
					// Create a new user record for the new organization
					const newUserRequest = {
						email: agencyUser.email,
						firstName: agencyUser.firstName || agencyUser.email,
						lastName: agencyUser.lastName,
						organizationId: newOrgId,
						isSecondaryAccount: true,
						roles: agencyUser.roles || ['admin'], // Copy roles or default to admin
						avatarColors: agencyUser.avatarColors
					};

					const newUser = await this.userRepository.create(newUserRequest);
					console.log(`Copied user ${agencyUser.email} to new organization ${newOrgId} with ID ${newUser.id}`);
				} else {
					console.log(`User ${agencyUser.email} already exists in organization ${newOrgId}, skipping`);
				}
			}

			console.log(`Successfully copied users from agency ${agencyOrgId} to new organization ${newOrgId}`);
		} catch (error) {
			console.error(`Error copying users from agency ${agencyOrgId} to new organization ${newOrgId}:`, error);
			// Don't throw error to avoid breaking the onboarding flow
		}
	}

	/**
	 * Copies organization settings from the agency organization to the new brand organization
	 * @param agencyOrgId - The agency organization ID
	 * @param newOrgId - The new brand organization ID
	 */
	private async copyOrganizationSettingsFromAgencyToNewOrg(agencyOrgId: number, newOrgId: number): Promise<void> {
		try {
			// Get all organization settings from the agency organization
			const agencySettings = await this.orgRepository.organizationSettings(agencyOrgId).find();

			console.log(`Found ${agencySettings.length} organization settings in agency organization ${agencyOrgId} to copy to new organization ${newOrgId}`);

			// Copy each setting to the new organization
			for (const setting of agencySettings) {
				// Check if setting already exists in the new organization
				const existingSetting = await this.orgRepository.organizationSettings(newOrgId).find({
					where: {
						key: setting.key
					}
				});

				if (existingSetting.length === 0) {
					// Create the setting in the new organization
					await this.orgRepository.organizationSettings(newOrgId).create({
						key: setting.key,
						value: setting.value,
						organizationId: newOrgId
					});
					console.log(`Copied organization setting '${setting.key}' to new organization ${newOrgId}`);
				} else {
					console.log(`Organization setting '${setting.key}' already exists in organization ${newOrgId}, skipping`);
				}
			}

			console.log(`Successfully copied organization settings from agency ${agencyOrgId} to new organization ${newOrgId}`);
		} catch (error) {
			console.error(`Error copying organization settings from agency ${agencyOrgId} to new organization ${newOrgId}:`, error);
			// Don't throw error to avoid breaking the onboarding flow
		}
	}

	/**
	 * Get pricing preview for adding a brand to an agency
	 * @param currentUserProfile - The current user profile
	 * @returns Pricing breakdown information
	 */
	@get('/onboard/agency/pricing-preview', {
		responses: {
			'200': {
				description: 'Agency pricing preview',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								currentPlan: { type: 'object' },
								currentBrandCount: { type: 'number' },
								newBrandCount: { type: 'number' },
								brandsIncludedLimit: { type: 'number' },
								currentPrice: { type: 'number' },
								newPrice: { type: 'number' },
								extraBrandPrice: { type: 'number' },
								extraBrandsAfterAdd: { type: 'number' }
							}
						}
					}
				}
			}
		}
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'support', 'customer'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async getAgencyPricingPreview(
		@inject(SecurityBindings.USER)
		currentUserProfile: UserProfile,
	): Promise<any> {
		const existingOrgUser = await this.userRepository.findById(currentUserProfile.id);
		const parentOrgId = existingOrgUser.organizationId;

		if (!parentOrgId) {
			throw new HttpErrors.BadRequest('User must belong to an organization');
		}

		// Get the agency organization and its plan
		const agencyOrg = await this.orgRepository.findById(parentOrgId);
		const agencyPlan = await this.orgPlanRepository.findOne({
			where: { orgId: parentOrgId, status: 'ACTIVE' },
			include: [{ relation: 'plan' }]
		});

		if (!agencyPlan || !agencyPlan.plan) {
			throw new HttpErrors.NotFound('No active plan found for agency');
		}

		// Get the brands-included limit for the agency's plan
		const brandsIncludedLimit = await this.getBrandsIncludedLimit(agencyPlan.planId);

		// Count the current number of brands (child organizations)
		const currentBrandCount = await this.getBrandCount(parentOrgId);
		const newBrandCount = currentBrandCount + 1;

		// Calculate current extra brands and new extra brands
		const currentExtraBrands = Math.max(0, currentBrandCount - brandsIncludedLimit);
		const newExtraBrands = Math.max(0, newBrandCount - brandsIncludedLimit);

		// Get the Extra Agency Brand plan price ($125)
		const extraBrandPlan = await this.planRepository.findById(17); // Extra Agency Brand plan ID
		let extraBrandPrice = extraBrandPlan?.price || 125;

		// Check if the agency has an Extra Agency Brand plan with priceOverride
		const agencyExtraBrandPlan = await this.orgPlanRepository.findOne({
			where: { orgId: parentOrgId, planId: 17 }, // Extra Agency Brand plan ID
			include: [{ relation: 'plan' }]
		});

		// Use priceOverride if available for Extra Agency Brand plan
		if (agencyExtraBrandPlan && agencyExtraBrandPlan.priceOverride) {
			extraBrandPrice = agencyExtraBrandPlan.priceOverride;
		}

		// Calculate current and new prices
		const basePlanPrice = agencyPlan.plan.price;
		const currentPrice = basePlanPrice + (currentExtraBrands * extraBrandPrice);
		const newPrice = basePlanPrice + (newExtraBrands * extraBrandPrice);

		return {
			currentPlan: {
				id: agencyPlan.plan.id,
				name: agencyPlan.plan.name,
				price: basePlanPrice
			},
			currentBrandCount,
			newBrandCount,
			brandsIncludedLimit,
			currentPrice,
			newPrice,
			extraBrandPrice,
			extraBrandsAfterAdd: newExtraBrands
		};
	}


	private async createHubspotEntry(org: Organization, user: any) {
		try {
			const hubspotClient = new hubspot.Client({
				accessToken: '********************************************',
				numberOfApiCallRetries: 3,
			});

			// 1. Create or find contact
			const contactObj = {
				properties: {
					email: user.email
				},
			};

			// Find or create contact
			let contact = await this.findExistingHubspotContact(user);
			if (!contact) {
				try {
					contact = await hubspotClient.crm.contacts.basicApi.create(contactObj);
				} catch(e) {
					console.error('Error creating contact:', e);
					return;
				}
			}

			// 2. Extract domain from email
			const emailDomain = user.email.split('@')[1];
			if (!emailDomain) {
				console.error('Invalid email format');
				return;
			}

			// 3. Search for existing company by domain
			const companySearchRequest = {
				filterGroups: [{
					filters: [{
						propertyName: 'domain',
						operator: 'EQ',
						value: emailDomain
					}]
				}],
				properties: ['name', 'domain']
			};

			let company;
			const companiesResponse = await hubspotClient.crm.companies.searchApi.doSearch(companySearchRequest);

			// 4. Create company if none exists
			if (companiesResponse.results.length === 0) {
				const companyObj = {
					properties: {
						name: org.name,
						domain: emailDomain,
						hs_lead_status: 'NEW',
						website: org.externalDomain ? `https://${org.externalDomain}` : undefined
					},
				};

				try {
					company = await hubspotClient.crm.companies.basicApi.create(companyObj);
				} catch(e) {
					console.error('Error creating company:', e);
					return;
				}
			} else {
				company = companiesResponse.results[0];
			}

			// 5. Create association between contact and company
			if (company && contact) {
				try {
					await hubspotClient.crm.associations.v4.basicApi.create(
						'companies',
						company.id,
						'contacts',
						contact.id,
						[{
							"associationCategory": "HUBSPOT_DEFINED",
							"associationTypeId": AssociationTypes.primaryCompanyToContact
						}]
					);
				} catch(e) {
					console.error('Error creating association:', e);
				}
			}

		} catch (e) {
			console.error('Error in HubSpot integration:', e);
		}
	}

	private async findExistingHubspotContact(user: any) {
		const hubspotClient = new hubspot.Client({
			accessToken: '********************************************',
			numberOfApiCallRetries: 3,
		})

		const email = user.email;

		// Step 1: Find the contact by email
		const filter = {
			propertyName: 'email',
			operator: 'EQ',
			value: email
		};
		const publicObjectSearchRequest = {
			filterGroups: [
			{
				filters: [filter]
			}
			],
			properties: ['email']
		};
		const contactsResponse = await hubspotClient.crm.contacts.searchApi.doSearch(publicObjectSearchRequest);

		return contactsResponse.results?.[0];
	}

	private async updateHubspotLead(org: Organization, user: any, status: string) {
		const hubspotClient = new hubspot.Client({
			accessToken: '********************************************',
			numberOfApiCallRetries: 3,
		})

		const email = user.email;

		// Step 1: Find the contact by email
		const filter = {
			propertyName: 'email',
			operator: 'EQ',
			value: email
		};
		const publicObjectSearchRequest = {
			filterGroups: [
			{
				filters: [filter]
			}
			],
			properties: ['email']
		};
		const contactsResponse = await hubspotClient.crm.contacts.searchApi.doSearch(publicObjectSearchRequest);
		const contact = contactsResponse.results[0];

		if (!contact) {
			console.log('Contact not found.');
			throw new HttpErrors.NotFound('User not found');
		}

		const contactId = contact.id;


		const associationsResponse = await hubspotClient.apiRequest({
			method: 'GET',
			path: `/crm/v3/objects/contacts/${contactId}?associations=company`
		});

		const associationResults = await associationsResponse.json();


		// Step 3: Filter to primary company (assuming a custom property 'is_primary' exists)
		let primaryCompanyId = null;
		for (const result of associationResults.associations.companies.results) {
			const companyId = result.id;
			if (result.type === "contact_to_company") {
				primaryCompanyId = companyId;
				break;
			}
		}

		if (!primaryCompanyId) {
			console.log('No primary company found.');
			throw new HttpErrors.NotFound('Company not found');
		}

		// Step 4: Update the hs_lead_status of the primary company to "In Progress"
		const updatePayload = {
			properties: {
				hs_lead_status: status
			}
		};
		await hubspotClient.crm.companies.basicApi.update(primaryCompanyId, updatePayload);


	}

	@get('/onboard/snippet-config-check', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: 'boolean',
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	async isGoalComplete(
		@injectUserOrgId() orgId: number
	): Promise<boolean> {
		const url = `/dev/goal/custom-event?allAddresses=true&address=anonymous&eventName=landing-page&orgId=${orgId}`;
		const signedRequest = getURL(url, 'GET', undefined, GOAL_API);
		const response = await fetch(`https://${GOAL_API}${url}`, signedRequest);


		const org  = await this.orgRepository.findById(orgId);
		org.snippetVerified = true;
		this.orgRepository.update(org).catch();

		const isComplete = await response.json();
		return isComplete != null;
	}


	@get('/onboard/domain-metadata', {
		responses: {
			'200': {
				description: 'DomainMetadata',
				content: {
					'application/json': {
						schema: 'boolean',
					},
				},
			},
		},
	})
	@skipGuardCheck()
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	async getDomainMetadata(
		@injectUserOrgId() orgId: number
	): Promise<object> {
		const org  = await this.orgRepository.findById(orgId);
		const domain = 'www.allbirds.com'; // org.externalDomain;

		if (!domain) {
			throw HttpErrors.NotFound('No domain specified on project');
		}

		const metascraper = require('metascraper')([
			require('metascraper-twitter')(),
			require('metascraper-instagram')(),
			require('metascraper-telegram')(),
			require('metascraper-youtube')(),
			require('metascraper-media-provider')(),
			require('@samirrayani/metascraper-shopping')(),
		  ])

		//   /**
		//    * The main logic
		//    */
		//   getContent('https://microlink.io')
		// 	.then(metascraper)
		// 	.then(metadata => console.log(metadata))
		// 	.then(browserless.close)

		// const response = await fetch(`https://${domain}`);
		// const html = await response.text();


		const url = `https://${domain}`;

		// const meta = await urlMetadata(url, {
		// 	includeResponseBody: true
		// });

		// const more = await metascraper({
		// 	html: meta.responseBody,
		// 	url
		// });

		return {
			// ...meta,
			// ...more
		};
	}

	@post('/onboard/adventure/update', {
		responses: {
		  '200': {
			description: 'Update chosenAdventure',
			content: {
			  'application/json': {
				schema: {},
			  },
			},
		  },
		},
	  })
	  @authenticate('jwt')
	  @authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	  })
	  @skipGuardCheck()
	  async updateChosenAdventure(
		@requestBody({
		  content: {
			'application/json': {
			  schema: {
				type: 'object',
				required: ['chosenAdventure'],
				properties: {
				  chosenAdventure: {
					type: 'string',
				  },
				},
			  },
			},
		  },
		})
		data: {chosenAdventure: string},
		@injectUserOrgId() orgId: number,
		@inject(SecurityBindings.USER, {optional: true}) user: any,
	  ): Promise<any> {

		const org  = await this.orgRepository.findById(orgId);

		org.chosenAdventure = data.chosenAdventure;
		await this.orgRepository.update(org);

		return {message: "chosenAdventure updated successfully"};
	  }

	@post('/onboard/self-service/upgrade', {
		responses: {
			'200': {
				description: 'upgrade',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async selfServiceUpgrade(
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		})
		data: any,
		@injectUserOrgId() orgId: number,
		@inject(SecurityBindings.USER, {optional: true}) user: any,
	): Promise<any> {



		const org  = await this.orgRepository.findById(orgId);

		// if (org.selfServiceUpgradeRequested) {
		// 	return {};
		// }

		// const sns = new AWS.SNS({ region: 'us-east-1' }); // replace with your desired AWS region

		// const params = {
		// 	Message: JSON.stringify({
		// 		type: "self-service-org-upgrade-request",
		// 		user: user.name,
		// 		orgId
		// 	}),
		// 	TopicArn: 'arn:aws:sns:us-east-1:831543322268:self-service-upgrade-request'
		// };

		// await sns.publish(params).promise();

		const userRecord = await this.userRepository.findById(user.id);
		await this.updateHubspotLead(org, userRecord, 'IN_PROGRESS');

		org.selfServiceUpgradeRequested = true;
		this.orgRepository.update(org).catch();

		return {};
	}

	@post('/onboard/customize-loyalty-bot', {
		responses: {
			'200': {
				description: 'customize loyalty bot',
				content: {
					'application/json': {
						schema: {},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async customizeLoyaltyBot(
		@requestBody({
			content: {
				'application/json': {
					schema: {}
				},
			},
		})
		data: any,
		@injectUserOrgId() orgId: number,
		@inject(SecurityBindings.USER, {optional: true}) user: any,
	): Promise<any> {
		const org  = await this.orgRepository.findById(orgId);

		org.questBrandColor = data.brandColor;
		org.questLaunchAvatarUrl = data.avatarUrl;
		org.questLaunchText = data.launchText;

		this.orgRepository.update(org).catch();

		return {};
	}





	@post('/onboard/organization-coupon-fix-test', {
		responses: {
			'200': {
				description: 'Org model instance',
				content: {'application/json': {schema: getModelSchemaRef(Organization)}},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async onboardCouponTest(
		@injectUserOrgId() orgId: number,
	): Promise<Organization> {
		const org = await this.orgRepository.findById(orgId);
		const newOrg = org;

		await this.discountCodeService.createDiscountCode(
			{
				id: 0,
				orgId: newOrg.id!,
				rewardCoupon: {
					amount: 1,
					amountType: 'dollars-off-coupon',
					expiresInDays: 1,
					combinesWithProducts: true,
					combinesWithShipping: true,
				}
			} as InventoryCouponWithRelations,
			'test-coupon',
			org.externalDomain!,
			newOrg.id!,
			0,
			true
		);

		return newOrg;
	}

	@post('/onboard/organization', {
		responses: {
			'200': {
				description: 'Org model instance',
				content: {'application/json': {schema: getModelSchemaRef(Organization)}},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async createOrganization(
		@requestBody({
			content: {
				'application/json': {
					schema: getModelSchemaRef(Organization, {
						title: 'NewOrg',
						exclude: ['id'],
					}),
				},
			},
		})
		org: Omit<Organization, 'id'>,
	): Promise<Organization> {
		const newOrg = await this.orgRepository.create(org);

		await this.orgPlanRepository.create({
			orgId: newOrg.id,
			planId: 15,
			status: 'ACTIVE',
		})
		// Call updateFeatureState
		await this.featureService.updateLoyaltyAndGwpEnabled(newOrg.id!);

		this.discountCodeService.createDiscountCode(
			{
				id: 0,
				orgId: newOrg.id!,
				rewardCoupon: {
					amount: 1,
					amountType: 'dollars-off-coupon',
					expiresInDays: 1,
					combinesWithProducts: true,
					combinesWithShipping: true,
				}
			} as InventoryCouponWithRelations,
			'test-coupon',
			org.externalDomain!,
			newOrg.id!,
			0,
			true
		).catch();

		return newOrg;
	}

	@patch('/onboard/users/{userId}', {
		responses: {
			'200': {
				description: 'User',
				content: {
					'application/json': {
						schema: {
							'x-ts-type': User,
						},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async updateUser(
		@param.path.string('userId')
		userId: number,

		@requestBody({description: 'update user'})
		user: Partial<any>,
	): Promise<void> {
		try {
			return await this.userRepository.updateById(userId, user);
		} catch (e) {
			return e;
		}
	}

	@post('/onboard/track-project', {
		responses: {
			'200': {
				description: 'creates new project to run metrics on',
				content: {
					'application/json': {
						schema: {
							type: 'object',
							properties: {
								uuid: {
									type: 'string',
								},
								name: {
									type: 'string',
								},
								projectid: {
									type: 'number',
								},
								smartcontracts: {
									type: 'array',
									items: {
										type: 'object',
										properties: {
											address: {
												type: 'string'
											},
											network: {
												type: 'string'
											},
											aggregate: {
												type: 'boolean'
											}
										}
									}
								},
								tokens: {
								 type: 'array',
								 items: {
									 type: 'object',
									 properties: {
										 address: {
											 type: 'string'
										 },
										 network: {
											 type: 'string'
										 }
									 }
								 }
								},
								nfts: {
								 type: 'array',
								 items: {
									 type: 'object',
									 properties: {
										 address: {
											 type: 'string'
										 },
										 network: {
											 type: 'string'
										 }
									 }
								 }
								}
							}
						},
					},
				},
			},
		},
	})
	@authenticate('jwt')
	@authorize({
		allowedRoles: ['admin', 'raleon-support'],
		voters: [basicAuthorization],
	})
	@skipGuardCheck()
	async createTrackedProject(
		@requestBody({
			content: {
				'application/json': {
					schema: {
						type: 'object',
						properties: {
							uuid: {
								type: 'string',
							},
							name: {
								type: 'string',
							},
							projectid: {
								type: 'number',
							},
							smartcontracts: {
								type: 'array',
								items: {
									type: 'object',
									properties: {
										address: {
											type: 'string'
										},
										network: {
											type: 'string'
										},
										aggregate: {
											type: 'boolean'
										}
									}
								}
							},
							tokens: {
								type: 'array',
								items: {
									type: 'object',
									properties: {
										address: {
											type: 'string'
										},
										network: {
											type: 'string'
										}
									}
								}
							},
							nfts: {
							 type: 'array',
							 items: {
								 type: 'object',
								 properties: {
									 address: {
										 type: 'string'
									 },
									 network: {
										 type: 'string'
									 }
								 }
							 }
							}
						}
					},
				},
			},
		})
		payload: any,
	): Promise<object> {
		let allContracts = []
		if (payload.smartcontracts) {
			for (let i = 0; i < payload.smartcontracts.length; i++) {
				allContracts.push({
					address: payload.smartcontracts[i].address.toLowerCase(),
					network: payload.smartcontracts[i].network,
					addresstype: 'smart-contract',
					aggregate: payload.smartcontracts[i].aggregate
				})
			}
		}
		if (payload.tokens) {
			for (let i = 0; i < payload.tokens.length; i++) {
				allContracts.push({
					address: payload.tokens[i].address.toLowerCase(),
					network: payload.tokens[i].network,
					addresstype: 'token'
				})
			}
		}
		if (payload.nfts) {
			for (let i = 0; i < payload.nfts.length; i++) {
				allContracts.push({
					address: payload.nfts[i].address.toLowerCase(),
					network: payload.nfts[i].network,
					addresstype: 'nft'
				})
			}
		}

		//Lets register all contracts with ingestion, these can fail if already registered
		for (let i = 0; i < allContracts.length; i++) {
			let response: any;
			try {
				const url = `/dev/address/register`;
				let addressData: any = {
					address: allContracts[i].address.toLowerCase(),
					network: allContracts[i].network,
					type: allContracts[i].addresstype
				}

				if(allContracts[i].addresstype == 'smart-contract') {
					addressData['aggregate'] = allContracts[i].aggregate;
				}

				const signedRequest = getURL(url, 'POST', addressData);
				response = await fetch(`https://${DATA_API}${url}`, signedRequest);
			} catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}

			const data: any = await response.text();
			console.log(data)
		}

		//Now lets register the project with ingestion
		let response: any;
		try {
			const url = `/dev/address/register`;
			let projectData: any = {
				address: payload.uuid,
				network: 'ANY',
				type: 'raleon-project',
				name: payload.name,
				data: {
					name: payload.name,
					smart_contracts: [
					],
					tokens: [
					],
					nfts: [
					],
					view: [
					]
				}
			}

			if (payload.smartcontracts) {
				for (let i = 0; i < payload.smartcontracts.length; i++) {
					projectData.data.smart_contracts.push({
						address: payload.smartcontracts[i].address.toLowerCase(),
						network: payload.smartcontracts[i].network
					})
				}
			}

			if (payload.tokens) {
				for (let i = 0; i < payload.tokens.length; i++) {
					projectData.data.tokens.push({
						address: payload.tokens[i].address.toLowerCase(),
						network: payload.tokens[i].network
					})
				}
			}

			if (payload.nfts) {
				for (let i = 0; i < payload.nfts.length; i++) {
					projectData.data.nfts.push({
						address: payload.nfts[i].address.toLowerCase(),
						network: payload.nfts[i].network
					})
				}
			}

			const signedRequest = getURL(url, 'POST', projectData);
			response = await fetch(`https://${DATA_API}${url}`, signedRequest);
		} catch (err) {
			console.log("Error: " + JSON.stringify(err));
		}

		const data: any = await response.text();
		console.log(data)

		//lets set the activation date if this succeeds
		if (response.status == 200) {
			let response: any;
			try {
				this.projectRepository.updateById(payload.projectid, {
					activationDate: new Date().toISOString()
				})
				console.log("Project activated")
			}
			catch (err) {
				console.log("Error: " + JSON.stringify(err));
			}
		}


		return {
			status: response.status,
			data: {
				uuid: payload.uuid,
				name: payload.name
			}
		};
	}

	private async setDefaultCurrency(currencyCode: string, orgId: number) {
		const supportedCurrency = await this.supportedCurrenciesRepository.findOne({
			where: {
				name: currencyCode
			}
		});

		if (supportedCurrency) {
			await this.currencyRepository.create({
				organizationId: orgId,
				supportedCurrenciesId: supportedCurrency.id
			});
		}
	}

	private async createDefaultAudiences(orgId: number): Promise<any> {

		const uuid = '99f2ed90-91ac-465c-bbc8-112d4664c882';
		const name = 'Top EVM';

		// const uuid = data.targetUsers === 'web3-gamers'
		// 	? '0fe83aea-9380-4dd7-8408-7458e4931a97'
		// 	: '99f2ed90-91ac-465c-bbc8-112d4664c882';

		// const name = data.targetUsers === 'web3-gamers'
		// 	? 'All Gamers'
		// 	: 'Top EVM';

		// get TopEVM project
		const project = await this.projectRepository.findOne({
			where: {
				uuid
			}
		});

		// let projectWithoutDataConnections = project;
		// delete (projectWithoutDataConnections as any).dataConnections;

		// create copy
		const newProject = await this.projectRepository.create({
			...project,
			organizationId: orgId,
			id: undefined,
			isAdmin: false,
		});

		// this.dataConnectionsRepository.create({
		// 	...(project?.dataConnections?.[0] || {}),
		// 	id: undefined,
		// 	projectId: newProject.id,
		// 	project_uuid: newProject.uuid,
		// 	name: 'Top EVM',
		// }).catch();

		const template = await this.segmentRepository.findOne({
			where: {
				orgid: 5,
				name
			}
		});

		const viewname = template!.viewname;
		const segment = await this.segmentRepository.create({
			name,
			"network": "ETH",
			"orgid": orgId,
			"queries": [
			  {
				"id": 0,
				"instruction": "source_project_users",
				"subject": {
				  "type": "PROJECT",
				  "value": uuid
				},
				"time": {},
				"required": {}
			  }
			],
			status: "PUBLISHED",
			viewname
		  } as any);

	}
}

type TokenOption = {
	createToken?: boolean,
	externalDomain?: string,
	currencyCode?: string,
	externalPlanDetails?: any,
}
